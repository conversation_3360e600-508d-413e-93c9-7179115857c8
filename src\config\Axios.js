import axios from 'axios';

const axiosInstance = axios.create();

axiosInstance.interceptors.request.use((config) => {
    const token = localStorage.getItem("token");
    if (token) {
        config.headers['Authorization'] = `Bearer ${token}`;
    }

    if (config.data instanceof FormData) {
        config.headers['Content-Type'] = 'multipart/form-data';

        // Log FormData contents for debugging
        console.log('FormData keys:', [...config.data.keys()]);
        console.log('FormData entries:', [...config.data.entries()].map(entry => {
            // Don't log large values like templates
            if (entry[0] === 'template' && typeof entry[1] === 'string' && entry[1].length > 100) {
                return [entry[0], `${entry[1].substring(0, 100)}... (${entry[1].length} chars)`];
            }
            return entry;
        }));
    } else {
        config.headers['Content-Type'] = 'application/json';

        // Log request data for debugging
        if (config.data && typeof config.data === 'object') {
            const dataCopy = {...config.data};

            // Don't log large values like templates
            if (dataCopy.template && typeof dataCopy.template === 'string' && dataCopy.template.length > 100) {
                dataCopy.template = `${dataCopy.template.substring(0, 100)}... (${dataCopy.template.length} chars)`;
            }

            console.log('Request data:', dataCopy);
        }
    }

    config.headers['Accept'] = 'application/json';

    console.log('Request config:', {
        url: config.url,
        method: config.method,
        headers: config.headers,
        hasData: !!config.data
    });

    return config;
});

// Set default baseURL if environment variable is not available
axiosInstance.defaults.baseURL = import.meta.env.VITE_BACKEND_URL || 'http://*************:8000/api';

export default axiosInstance;

axiosInstance.interceptors.response.use(
    (response) => {
        // Log response for debugging
        console.log('Response from:', response.config.url, {
            status: response.status,
            statusText: response.statusText,
            data: response.data
        });

        // Skip redirects for registration endpoint
        if (response.config.url.includes('/register')) {
            return response;
        }

        if (response.config.url.includes('/packages/purchase/') && response.status === 200) {
            const sessionId = response.data.sessionId;
            if (sessionId) {
                window.location.href = `https://checkout.stripe.com/pay/${sessionId}`;
                return;
            }
        }

        // Auto-redirect based on user_type ONLY (role field ignored)
        if (response.data.user && response.data.user.user_type) {
            console.log('🔍 Axios Interceptor - URL:', response.config.url);
            console.log('🔍 Axios Interceptor - User Type:', response.data.user.user_type);
            
            // Skip auto-redirect for authentication-related endpoints
            const skipUrls = ['/login', '/two-factor', '/register', '/verify', '/reset'];
            const shouldSkip = skipUrls.some(url => response.config.url.includes(url));
            
            if (shouldSkip) {
                console.log('🚫 Axios Interceptor - Skipping redirect for auth endpoint:', response.config.url);
                return response;
            }
            
            console.log('✅ Axios Interceptor - Processing auto-redirect');
            const userType = response.data.user.user_type;
            
            console.log('🔍 Axios Interceptor - User Data (user_type ONLY):', {
                user_type: userType,
                IGNORED_role: response.data.user.role,
                user_id: response.data.user.id
            });
            
            // Only update localStorage if not already set by login flow
            if (!localStorage.getItem("user_type") && userType) {
                localStorage.setItem("user_type", userType);
            }
            // IGNORING role field completely

            // Redirect based on user_type ONLY
            switch(userType) {
                case 'user':
                    window.location.href = '/users/dashboard';
                    break;
                case 'admin':
                    console.log('🎯 Axios Interceptor: Admin detected (user_type=admin) - redirecting to admin dashboard');
                    window.location.href = '/admin/dashboard';
                    break;
                case 'manager':
                    console.log('🎯 Axios Interceptor: Manager detected (user_type=manager) - redirecting to manager dashboard');
                    window.location.href = '/manager/dashboard';
                    break;
                default:
                    console.log('🎯 Axios Interceptor: Unknown user_type:', userType, '- redirecting to login');
                    window.location.href = '/login';
            }
        }

        return response;
    },
    (error) => {
        console.error('API Error:', {
            url: error.config?.url,
            method: error.config?.method,
            status: error.response?.status,
            statusText: error.response?.statusText,
            data: error.response?.data,
            message: error.message
        });

        const token = localStorage.getItem("token");
        if (error.response && (error.response.status === 401 || error.response.status === 403) && token) {
            localStorage.clear();
            window.location.href = '/login';
        }
        return Promise.reject(error);
    }
);
