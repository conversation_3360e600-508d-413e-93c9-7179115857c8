import React, { useState, useEffect, useMemo } from 'react';
import { Dialog } from 'primereact/dialog';
import { But<PERSON> } from 'primereact/button';
import { InputText } from 'primereact/inputtext';
import { Dropdown } from 'primereact/dropdown';
import { DataTable } from 'primereact/datatable';
import { Column } from 'primereact/column';
import { Checkbox } from 'primereact/checkbox';
import { Calendar } from 'primereact/calendar';
import { motion } from 'framer-motion';
import { FiSearch, FiUsers, FiFilter } from 'react-icons/fi';
import { useLayout } from '@contexts/LayoutContext';
import { mockMembers, mockCardTypes } from '@data/mockEventsData';

const AssignCardsModal = ({ visible, onHide, event, onAssign }) => {
    const { isMobile } = useLayout();
    
    const [selectedMembers, setSelectedMembers] = useState([]);
    const [searchTerm, setSearchTerm] = useState('');
    const [departmentFilter, setDepartmentFilter] = useState(null);
    const [selectedCardType, setSelectedCardType] = useState(null);
    const [validFrom, setValidFrom] = useState(null);
    const [validUntil, setValidUntil] = useState(null);
    const [loading, setLoading] = useState(false);
    const [errors, setErrors] = useState({});

    // Get unique departments for filter
    const departments = useMemo(() => {
        const depts = [...new Set(mockMembers.map(member => member.department))];
        return depts.map(dept => ({ label: dept, value: dept }));
    }, []);

    // Filter members based on search and department
    const filteredMembers = useMemo(() => {
        let filtered = mockMembers;

        if (searchTerm) {
            filtered = filtered.filter(member =>
                member.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                member.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
                member.role.toLowerCase().includes(searchTerm.toLowerCase())
            );
        }

        if (departmentFilter) {
            filtered = filtered.filter(member => member.department === departmentFilter.value);
        }

        // Exclude members who already have cards for this event
        if (event?.temporaryCards) {
            const assignedMemberIds = event.temporaryCards.map(card => card.memberId);
            filtered = filtered.filter(member => !assignedMemberIds.includes(member.id));
        }

        return filtered;
    }, [searchTerm, departmentFilter, event]);

    // Initialize form when modal opens
    useEffect(() => {
        if (visible && event) {
            setSelectedMembers([]);
            setSearchTerm('');
            setDepartmentFilter(null);
            setSelectedCardType(null);
            setValidFrom(new Date(event.startDate));
            setValidUntil(new Date(event.endDate));
            setErrors({});
        }
    }, [visible, event]);

    const validateForm = () => {
        const newErrors = {};

        if (selectedMembers.length === 0) {
            newErrors.members = 'Please select at least one member';
        }

        if (!selectedCardType) {
            newErrors.cardType = 'Please select a card type';
        }

        if (!validFrom) {
            newErrors.validFrom = 'Please select a valid from date';
        }

        if (!validUntil) {
            newErrors.validUntil = 'Please select a valid until date';
        }

        if (validFrom && validUntil && validUntil <= validFrom) {
            newErrors.validUntil = 'Valid until date must be after valid from date';
        }

        setErrors(newErrors);
        return Object.keys(newErrors).length === 0;
    };

    const handleAssign = async () => {
        if (!validateForm()) {
            return;
        }

        setLoading(true);

        try {
            const cardAssignments = selectedMembers.map(member => ({
                id: Date.now() + Math.random(), // Generate temporary ID
                memberId: member.id,
                memberName: member.name,
                memberEmail: member.email,
                cardType: selectedCardType.name,
                cardTypeId: selectedCardType.id,
                assignedAt: new Date().toISOString(),
                validFrom: validFrom.toISOString(),
                validUntil: validUntil.toISOString(),
                status: 'active',
                accessLevel: selectedCardType.permissions?.[0] || 'standard'
            }));

            await onAssign(event.id, cardAssignments);
        } catch (error) {
            console.error('Error assigning cards:', error);
        } finally {
            setLoading(false);
        }
    };

    // Template functions for DataTable
    const memberTemplate = (rowData) => (
        <div className="flex items-center gap-3">
            <div className="w-10 h-10 rounded-full bg-blue-500 flex items-center justify-center">
                <span className="text-white font-medium">
                    {rowData.name?.charAt(0)?.toUpperCase()}
                </span>
            </div>
            <div>
                <div className="font-medium">{rowData.name}</div>
                <div className="text-sm text-gray-500">{rowData.email}</div>
            </div>
        </div>
    );

    const departmentTemplate = (rowData) => (
        <div>
            <div className="font-medium">{rowData.department}</div>
            <div className="text-sm text-gray-500">{rowData.role}</div>
        </div>
    );

    const selectionTemplate = (rowData) => (
        <Checkbox
            checked={selectedMembers.some(member => member.id === rowData.id)}
            onChange={(e) => {
                if (e.checked) {
                    setSelectedMembers([...selectedMembers, rowData]);
                } else {
                    setSelectedMembers(selectedMembers.filter(member => member.id !== rowData.id));
                }
                // Clear members error when selection changes
                if (errors.members) {
                    setErrors(prev => ({ ...prev, members: '' }));
                }
            }}
        />
    );

    const renderFooter = () => (
        <div className="flex justify-between items-center">
            <div className="text-sm text-gray-600">
                {selectedMembers.length} member(s) selected
            </div>
            <div className="flex gap-2">
                <Button
                    label="Cancel"
                    className="gray-btn"
                    onClick={onHide}
                    disabled={loading}
                />
                <Button
                    label="Assign Cards"
                    className="main-btn"
                    onClick={handleAssign}
                    loading={loading}
                    disabled={loading || selectedMembers.length === 0}
                />
            </div>
        </div>
    );

    if (!event) return null;

    return (
        <Dialog
            header={`Assign Cards - ${event.name}`}
            visible={visible}
            style={isMobile ? { width: "95vw", height: "90vh" } : { width: "80vw", minWidth: '900px' }}
            breakpoints={isMobile ? {} : { '960px': '90vw', '641px': '95vw' }}
            modal
            onHide={onHide}
            footer={renderFooter()}
            maximizable={false}
            resizable={false}
            contentStyle={{ maxHeight: isMobile ? 'calc(90vh - 160px)' : 'auto', overflow: 'auto' }}
        >
            <div className="space-y-6">
                {/* Card Assignment Configuration */}
                <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.3 }}
                    className="bg-gray-50 p-4 rounded-lg"
                >
                    <h4 className="text-lg font-semibold text-gray-900 mb-4">Card Assignment Details</h4>
                    
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                        <div className="field">
                            <label htmlFor="cardType" className="form-label text-sm font-medium">
                                Card Type *
                            </label>
                            <Dropdown
                                id="cardType"
                                value={selectedCardType}
                                options={mockCardTypes}
                                onChange={(e) => {
                                    setSelectedCardType(e.value);
                                    if (errors.cardType) {
                                        setErrors(prev => ({ ...prev, cardType: '' }));
                                    }
                                }}
                                optionLabel="name"
                                placeholder="Select card type"
                                className={`w-full ${errors.cardType ? 'p-invalid' : ''}`}
                            />
                            {errors.cardType && <small className="p-error">{errors.cardType}</small>}
                        </div>

                        <div className="field">
                            <label htmlFor="validFrom" className="form-label text-sm font-medium">
                                Valid From *
                            </label>
                            <Calendar
                                id="validFrom"
                                value={validFrom}
                                onChange={(e) => {
                                    setValidFrom(e.value);
                                    if (errors.validFrom) {
                                        setErrors(prev => ({ ...prev, validFrom: '' }));
                                    }
                                }}
                                placeholder="Select start date"
                                showIcon
                                className={`w-full ${errors.validFrom ? 'p-invalid' : ''}`}
                                minDate={new Date()}
                            />
                            {errors.validFrom && <small className="p-error">{errors.validFrom}</small>}
                        </div>

                        <div className="field">
                            <label htmlFor="validUntil" className="form-label text-sm font-medium">
                                Valid Until *
                            </label>
                            <Calendar
                                id="validUntil"
                                value={validUntil}
                                onChange={(e) => {
                                    setValidUntil(e.value);
                                    if (errors.validUntil) {
                                        setErrors(prev => ({ ...prev, validUntil: '' }));
                                    }
                                }}
                                placeholder="Select end date"
                                showIcon
                                className={`w-full ${errors.validUntil ? 'p-invalid' : ''}`}
                                minDate={validFrom || new Date()}
                            />
                            {errors.validUntil && <small className="p-error">{errors.validUntil}</small>}
                        </div>
                    </div>

                    {selectedCardType && (
                        <div className="mt-4 p-3 bg-white rounded-lg border border-gray-200">
                            <div className="flex items-center gap-2 mb-2">
                                <div 
                                    className="w-4 h-4 rounded-full"
                                    style={{ backgroundColor: selectedCardType.color }}
                                />
                                <span className="font-medium">{selectedCardType.name}</span>
                            </div>
                            <p className="text-sm text-gray-600 mb-2">{selectedCardType.description}</p>
                            <div className="flex flex-wrap gap-1">
                                {selectedCardType.permissions?.map((permission, index) => (
                                    <span 
                                        key={index}
                                        className="px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded-full"
                                    >
                                        {permission.replace('_', ' ')}
                                    </span>
                                ))}
                            </div>
                        </div>
                    )}
                </motion.div>

                {/* Member Selection */}
                <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.3, delay: 0.1 }}
                    className="bg-white rounded-lg border border-gray-200"
                >
                    <div className="p-4 border-b border-gray-200">
                        <h4 className="text-lg font-semibold text-gray-900 mb-4">Select Members</h4>
                        
                        {/* Search and Filter */}
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div className="relative">
                                <FiSearch className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={16} />
                                <InputText
                                    placeholder="Search members..."
                                    value={searchTerm}
                                    onChange={(e) => setSearchTerm(e.target.value)}
                                    className="w-full pl-10"
                                />
                            </div>
                            
                            <Dropdown
                                value={departmentFilter}
                                options={departments}
                                onChange={(e) => setDepartmentFilter(e.value)}
                                placeholder="Filter by department"
                                showClear
                                className="w-full"
                            />
                        </div>

                        {errors.members && (
                            <div className="mt-2">
                                <small className="p-error">{errors.members}</small>
                            </div>
                        )}
                    </div>

                    <DataTable
                        value={filteredMembers}
                        className="border-t-0"
                        emptyMessage="No members found"
                        responsiveLayout="stack"
                        breakpoint="960px"
                        scrollable
                        scrollHeight="400px"
                    >
                        <Column body={selectionTemplate} style={{ width: '3rem' }} />
                        <Column body={memberTemplate} header="Member" />
                        <Column body={departmentTemplate} header="Department & Role" />
                    </DataTable>
                </motion.div>

                {/* Selected Members Summary */}
                {selectedMembers.length > 0 && (
                    <motion.div
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ duration: 0.3, delay: 0.2 }}
                        className="bg-blue-50 p-4 rounded-lg border border-blue-200"
                    >
                        <div className="flex items-center gap-2 mb-2">
                            <FiUsers className="text-blue-600" size={16} />
                            <span className="font-medium text-blue-900">
                                {selectedMembers.length} Member(s) Selected
                            </span>
                        </div>
                        <div className="flex flex-wrap gap-2">
                            {selectedMembers.map(member => (
                                <span 
                                    key={member.id}
                                    className="px-3 py-1 bg-blue-100 text-blue-800 text-sm rounded-full"
                                >
                                    {member.name}
                                </span>
                            ))}
                        </div>
                    </motion.div>
                )}
            </div>
        </Dialog>
    );
};

export default AssignCardsModal;
