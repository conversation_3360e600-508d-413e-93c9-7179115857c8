import React, { useState, useEffect, useRef } from 'react';
import { Dialog } from 'primereact/dialog';
import { <PERSON><PERSON> } from 'primereact/button';
import { InputText } from 'primereact/inputtext';
import { motion } from 'framer-motion';
import { FiCreditCard, FiSearch, FiArrowRight, FiArrowLeft, FiX, FiCheck } from 'react-icons/fi';
import { useLayout } from '@contexts/LayoutContext';

// Import drag and drop components
import {
    DndContext,
    closestCenter,
    PointerSensor,
    useSensor,
    useSensors,
    useDroppable,
    DragOverlay
} from '@dnd-kit/core';
import {
    SortableContext,
    useSortable,
    verticalListSortingStrategy
} from '@dnd-kit/sortable';
import { CSS } from '@dnd-kit/utilities';

// Mock data for available cards
const mockAvailableCards = [
    {
        id: 'card-001',
        name: 'Business Card #001',
        number: 'BC001',
        card_type: { name: 'Standard Access', type_of_connection: 'NFC' },
        status: 'available',
        manager_name: null
    },
    {
        id: 'card-002',
        name: 'Business Card #002',
        number: 'BC002',
        card_type: { name: 'VIP Access', type_of_connection: 'Bluetooth' },
        status: 'available',
        manager_name: null
    },
    {
        id: 'card-003',
        name: 'Business Card #003',
        number: 'BC003',
        card_type: { name: 'Standard Access', type_of_connection: 'NFC' },
        status: 'available',
        manager_name: null
    },
    {
        id: 'card-004',
        name: 'Business Card #004',
        number: 'BC004',
        card_type: { name: 'Media Pass', type_of_connection: 'NFC' },
        status: 'available',
        manager_name: null
    },
    {
        id: 'card-005',
        name: 'Business Card #005',
        number: 'BC005',
        card_type: { name: 'Workshop Access', type_of_connection: 'Bluetooth' },
        status: 'available',
        manager_name: null
    }
];

// Mock data for assigned cards
const mockAssignedCards = [
    {
        id: 'card-101',
        name: 'Business Card #101',
        number: 'BC101',
        card_type: { name: 'VIP Access', type_of_connection: 'NFC' },
        status: 'assigned',
        assignedTo: 'John Doe',
        assignedAt: '2024-08-01T10:00:00Z'
    },
    {
        id: 'card-102',
        name: 'Business Card #102',
        number: 'BC102',
        card_type: { name: 'Standard Access', type_of_connection: 'Bluetooth' },
        status: 'assigned',
        assignedTo: 'Jane Smith',
        assignedAt: '2024-08-01T11:00:00Z'
    }
];

// Droppable Container Component
const DroppableContainer = ({ id, children, style, className }) => {
    const { setNodeRef, isOver } = useDroppable({ id });
    
    return (
        <div
            ref={setNodeRef}
            style={{
                ...style,
                backgroundColor: isOver ? 'rgba(130, 245, 112, 0.1)' : style.backgroundColor,
                borderColor: isOver ? '#82f570' : style.borderColor,
            }}
            className={className}
        >
            {children}
        </div>
    );
};

// Draggable Card Component
const DraggableCard = ({ card, id, from, isOverlay, isMobile, onDoubleClick }) => {
    const { attributes, listeners, setNodeRef, transform, isDragging } = useSortable({ id });
    
    const style = {
        transform: CSS.Transform.toString(transform),
        opacity: isDragging ? 0.5 : 1,
        transition: 'all 0.2s ease',
        cursor: isDragging ? 'grabbing' : 'grab',
    };

    const cardTypeColor = {
        'VIP Access': '#FFD700',            // To be edited by Ammar & Rafat at some point
        'Standard Access': '#4A90E2',
        'Media Pass': '#E94B3C',
        'Workshop Access': '#50C878'
    };

    return (
        <div
            ref={setNodeRef}
            style={style}
            {...attributes}
            {...listeners}
            className={`p-3 mb-2 rounded-lg border transition-all ${
                from === 'available' 
                    ? 'bg-blue-50 border-blue-200 hover:bg-blue-100' 
                    : 'bg-green-50 border-green-200 hover:bg-green-100'
            }`}
            onDoubleClick={() => onDoubleClick && onDoubleClick(card, from)}
        >
            <div className="flex items-center justify-between">
                <div className="flex-1">
                    <div className="flex items-center gap-2">
                        <FiCreditCard 
                            className={from === 'available' ? 'text-blue-600' : 'text-green-600'} 
                            size={16} 
                        />
                        <span className="font-medium text-gray-900">{card.name}</span>
                    </div>
                    <div className="text-sm text-gray-500 mt-1">
                        {card.number} • {card.card_type?.name} • {card.card_type?.type_of_connection}
                    </div>
                    {card.assignedTo && (
                        <div className="text-xs text-green-600 mt-1">
                            Assigned to: {card.assignedTo}
                        </div>
                    )}
                </div>
                <div 
                    className="w-3 h-3 rounded-full"
                    style={{ backgroundColor: cardTypeColor[card.card_type?.name] || '#6B7280' }}
                />
            </div>
        </div>
    );
};

const CardsInventoryModal = ({ visible, onHide, event, onUpdateEvent }) => {
    const { isMobile } = useLayout();
    const [availableCards, setAvailableCards] = useState(mockAvailableCards);
    const [assignedCards, setAssignedCards] = useState(mockAssignedCards);
    const [searchQuery, setSearchQuery] = useState('');
    const [activeId, setActiveId] = useState(null);
    const [loading, setLoading] = useState(false);
    const popAudioRef = useRef();

    // Configure drag sensors
    const sensors = useSensors(
        useSensor(PointerSensor, {
            activationConstraint: {
                distance: 8,
            },
        })
    );

    // Filter cards based on search query
    const filteredAvailableCards = availableCards.filter(card =>
        card.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        card.number.toLowerCase().includes(searchQuery.toLowerCase()) ||
        card.card_type?.name.toLowerCase().includes(searchQuery.toLowerCase())
    );

    const filteredAssignedCards = assignedCards.filter(card =>
        card.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        card.number.toLowerCase().includes(searchQuery.toLowerCase()) ||
        card.card_type?.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        card.assignedTo?.toLowerCase().includes(searchQuery.toLowerCase())
    );

    // Handle drag start
    const handleDragStart = (event) => {
        setActiveId(event.active.id);
    };

    // Handle drag end
    const handleDragEnd = (event) => {
        const { active, over } = event;
        
        if (!over) {
            setActiveId(null);
            return;
        }

        const activeCard = [...availableCards, ...assignedCards].find(card => card.id === active.id);
        const sourceContainer = availableCards.find(card => card.id === active.id) ? 'available' : 'assigned';
        const targetContainer = over.id;

        if (sourceContainer !== targetContainer && activeCard) {
            // Play sound effect
            if (popAudioRef.current) {
                popAudioRef.current.currentTime = 0;
                popAudioRef.current.play().catch(() => {});
            }

            if (targetContainer === 'assigned-list') {
                // Move from available to assigned
                setAvailableCards(prev => prev.filter(card => card.id !== activeCard.id));
                setAssignedCards(prev => [...prev, {
                    ...activeCard,
                    status: 'assigned',
                    assignedTo: 'Event Assignment',
                    assignedAt: new Date().toISOString()
                }]);
            } else if (targetContainer === 'available-list') {
                // Move from assigned to available
                setAssignedCards(prev => prev.filter(card => card.id !== activeCard.id));
                setAvailableCards(prev => [...prev, {
                    ...activeCard,
                    status: 'available',
                    assignedTo: null,
                    assignedAt: null
                }]);
            }
        }

        setActiveId(null);
    };

    // Handle double click to move cards
    const handleDoubleClick = (card, from) => {
        if (from === 'available') {
            setAvailableCards(prev => prev.filter(c => c.id !== card.id));
            setAssignedCards(prev => [...prev, {
                ...card,
                status: 'assigned',
                assignedTo: 'Event Assignment',
                assignedAt: new Date().toISOString()
            }]);
        } else {
            setAssignedCards(prev => prev.filter(c => c.id !== card.id));
            setAvailableCards(prev => [...prev, {
                ...card,
                status: 'available',
                assignedTo: null,
                assignedAt: null
            }]);
        }
    };

    // Handle save changes
    const handleSaveChanges = async () => {
        setLoading(true);
        try {
            // Simulate API call
            await new Promise(resolve => setTimeout(resolve, 1000));
            
            // Update event with new card assignments
            if (onUpdateEvent) {
                const updatedEvent = {
                    ...event,
                    temporaryCards: assignedCards.map(card => ({
                        id: card.id,
                        cardId: card.id,
                        cardName: card.name,
                        cardNumber: card.number,
                        cardType: card.card_type?.name,
                        assignedAt: card.assignedAt,
                        status: 'active'
                    }))
                };
                onUpdateEvent(updatedEvent);
            }
            
            onHide();
        } catch (error) {
            console.error('Error saving card assignments:', error);
        } finally {
            setLoading(false);
        }
    };

    if (!event) return null;

    return (
        <>           
            <Dialog
                header={
                    <div className="flex items-center gap-2">
                        <FiCreditCard className="text-blue-600" size={20} />
                        <span>Cards Inventory - {event.name}</span>
                    </div>
                }
                visible={visible}
                style={isMobile ? { width: "95vw", height: "90vh" } : { width: "90vw", maxWidth: '1200px' }}
                breakpoints={isMobile ? {} : { '960px': '95vw', '641px': '95vw' }}
                modal
                onHide={onHide}
                maximizable={false}
                resizable={false}
                contentStyle={{ 
                    maxHeight: isMobile ? 'calc(90vh - 160px)' : 'calc(90vh - 120px)', 
                    overflow: 'hidden',
                    padding: isMobile ? '15px' : '20px'
                }}
                footer={
                    <div className="flex justify-between items-center">
                        <div className="text-sm text-gray-600">
                            {assignedCards.length} card(s) assigned to event
                        </div>
                        <div className="flex gap-2">
                            <Button
                                label="Cancel"
                                className="gray-btn"
                                onClick={onHide}
                                disabled={loading}
                                style={{
                                    backgroundColor: 'white',
                                    color: 'black',
                                    border: '1px solid #d1d5db',
                                    padding: '8px 16px',
                                    borderRadius: '6px',
                                    transition: 'all 0.2s ease'
                                }}
                            />
                            <Button
                                label="Save Changes"
                                loading={loading}
                                onClick={handleSaveChanges}
                                style={{
                                    backgroundColor: '#00c3ac',
                                    color: '#ffffffff',
                                    border: 'none',
                                    padding: '8px 16px',
                                    borderRadius: '6px',
                                    fontWeight: '600',
                                    transition: 'all 0.2s ease'
                                }}
                                onMouseEnter={(e) => {
                                    e.target.style.transform = 'translateY(-1px)';
                                    e.target.style.boxShadow = '0 4px 8px rgba(0, 0, 0, 0.1)';
                                }}
                                onMouseLeave={(e) => {
                                    e.target.style.transform = 'translateY(0)';
                                    e.target.style.boxShadow = 'none';
                                }}
                            />
                        </div>
                    </div>
                }
            >
                <div className="space-y-4 h-full">
                    {/* Search Bar */}
                    <motion.div
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ duration: 0.3 }}
                        className="relative"
                    >
                        <FiSearch className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={16} />
                        <InputText
                            placeholder="Search cards by name, number, or type..."
                            value={searchQuery}
                            onChange={(e) => setSearchQuery(e.target.value)}
                            className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                        />
                    </motion.div>

                    {/* Drag and Drop Context */}
                    <DndContext
                        sensors={sensors}
                        collisionDetection={closestCenter}
                        onDragStart={handleDragStart}
                        onDragEnd={handleDragEnd}
                    >
                        <div className={`grid gap-6 h-full ${isMobile ? 'grid-cols-1' : 'grid-cols-2'}`}>
                            {/* Available Cards Section */}
                            <SortableContext id="available-list" items={filteredAvailableCards.map(c => c.id)} strategy={verticalListSortingStrategy}>
                                <DroppableContainer
                                    id="available-list"
                                    className="flex flex-col h-full"
                                    style={{
                                        minHeight: isMobile ? 300 : 400,
                                        background: 'linear-gradient(135deg, #f0f9ff 0%, #e0e7ff 100%)',
                                        borderRadius: 12,
                                        border: '2px solid #60a5fa',
                                        padding: 16,
                                        overflow: 'hidden'
                                    }}
                                >
                                    <motion.div
                                        initial={{ opacity: 0, y: 20 }}
                                        animate={{ opacity: 1, y: 0 }}
                                        transition={{ duration: 0.3, delay: 0.1 }}
                                        className="flex items-center gap-3 mb-4"
                                    >
                                        <FiCreditCard className="text-blue-600" size={20} />
                                        <h3 className="font-bold text-blue-700 text-lg">Available Cards</h3>
                                        <span className="text-xs text-blue-600 bg-blue-100 rounded-full px-2 py-1">
                                            {filteredAvailableCards.length} cards
                                        </span>
                                    </motion.div>

                                    <div className="flex-1 overflow-y-auto">
                                        {filteredAvailableCards.length === 0 ? (
                                            <div className="text-center text-gray-400 py-8">
                                                {searchQuery ? 'No cards match your search' : 'No available cards'}
                                            </div>
                                        ) : (
                                            filteredAvailableCards.map(card => (
                                                <DraggableCard
                                                    key={card.id}
                                                    card={card}
                                                    id={card.id}
                                                    from="available"
                                                    isOverlay={false}
                                                    isMobile={isMobile}
                                                    onDoubleClick={handleDoubleClick}
                                                />
                                            ))
                                        )}
                                    </div>

                                    {!isMobile && (
                                        <div className="text-xs text-blue-600 mt-2 text-center">
                                            Drag cards to assign or double-click to move
                                        </div>
                                    )}
                                </DroppableContainer>
                            </SortableContext>

                            {/* Assigned Cards Section */}
                            <SortableContext id="assigned-list" items={filteredAssignedCards.map(c => c.id)} strategy={verticalListSortingStrategy}>
                                <DroppableContainer
                                    id="assigned-list"
                                    className="flex flex-col h-full"
                                    style={{
                                        minHeight: isMobile ? 300 : 400,
                                        background: 'linear-gradient(135deg, #f0fdf4 0%, #bbf7d0 100%)',
                                        borderRadius: 12,
                                        border: '2px solid #22c55e',
                                        padding: 16,
                                        overflow: 'hidden'
                                    }}
                                >
                                    <motion.div
                                        initial={{ opacity: 0, y: 20 }}
                                        animate={{ opacity: 1, y: 0 }}
                                        transition={{ duration: 0.3, delay: 0.2 }}
                                        className="flex items-center gap-3 mb-4"
                                    >
                                        <FiCheck className="text-green-600" size={20} />
                                        <h3 className="font-bold text-green-700 text-lg">Assigned to Event</h3>
                                        <span className="text-xs text-green-600 bg-green-100 rounded-full px-2 py-1">
                                            {filteredAssignedCards.length} cards
                                        </span>
                                    </motion.div>

                                    <div className="flex-1 overflow-y-auto">
                                        {filteredAssignedCards.length === 0 ? (
                                            <div className="text-center text-gray-400 py-8">
                                                {searchQuery ? 'No assigned cards match your search' : 'No cards assigned to this event'}
                                                <div className="text-xs mt-2">
                                                    Drag cards from the left to assign them
                                                </div>
                                            </div>
                                        ) : (
                                            filteredAssignedCards.map(card => (
                                                <DraggableCard
                                                    key={card.id}
                                                    card={card}
                                                    id={card.id}
                                                    from="assigned"
                                                    isOverlay={false}
                                                    isMobile={isMobile}
                                                    onDoubleClick={handleDoubleClick}
                                                />
                                            ))
                                        )}
                                    </div>

                                    {!isMobile && (
                                        <div className="text-xs text-green-600 mt-2 text-center">
                                            Drag cards back to unassign or double-click to move
                                        </div>
                                    )}
                                </DroppableContainer>
                            </SortableContext>
                        </div>

                        {/* Drag Overlay */}
                        <DragOverlay>
                            {activeId ? (
                                <DraggableCard
                                    card={[...availableCards, ...assignedCards].find(card => card.id === activeId)}
                                    id={activeId}
                                    from="overlay"
                                    isOverlay={true}
                                    isMobile={isMobile}
                                />
                            ) : null}
                        </DragOverlay>
                    </DndContext>

                    {/* Mobile Instructions */}
                    {isMobile && (
                        <motion.div
                            initial={{ opacity: 0, y: 20 }}
                            animate={{ opacity: 1, y: 0 }}
                            transition={{ duration: 0.3, delay: 0.3 }}
                            className="bg-gray-50 p-3 rounded-lg border border-gray-200"
                        >
                            <div className="text-sm text-gray-600 text-center">
                                <div className="flex items-center justify-center gap-2 mb-1">
                                    <FiArrowRight size={14} />
                                    <span>Double-tap cards to move between sections</span>
                                </div>
                                <div className="text-xs text-gray-500">
                                    Or drag and drop to assign/unassign cards
                                </div>
                            </div>
                        </motion.div>
                    )}
                </div>
            </Dialog>
        </>
    );
};

export default CardsInventoryModal;
