import React, { useState, useRef, useEffect } from 'react';
import { InputText } from 'primereact/inputtext';
import { Password } from 'primereact/password';
import { But<PERSON> } from 'primereact/button';
import { Toast } from 'primereact/toast';
import { Image } from 'primereact/image';
import { QRCodeSVG } from 'qrcode.react';
import { Dropdown } from 'primereact/dropdown';
import { classNames } from 'primereact/utils';
import axiosInstance from "../../../config/Axios";
import { 
    useSendEmailVerificationForProfileMutation, 
    useVerifyEmailForProfileMutation,
    useResendEmailVerificationForProfileMutation 
} from '../../../quires';
import { userAPI } from '../../../utils/api';
// import jsQR from 'jsqr'; // Removed

// API URL for image storage
const API_URL = import.meta.env.VITE_REACT_APP_API_URL || 'http://192.168.88.79:8000';

// Default user image
const DEFAULT_USER_IMAGE = 'https://storage.inknull.com/uploads/user-image-14-591-1751789627.png';

function SettingsIndex() {
    const toast = useRef(null);
    const fileInputRef = useRef(null);
    // const videoRef = useRef(null); // Removed
    // const canvasRef = useRef(null); // Removed
    const [isEditingProfile, setIsEditingProfile] = useState(false);
    const [isEditingPassword, setIsEditingPassword] = useState(false);
    const [originalName, setOriginalName] = useState('');
    const [originalEmail, setOriginalEmail] = useState('');
    const [loading, setLoading] = useState(false);
    const [imageLoading, setImageLoading] = useState(false);
    const [errors, setErrors] = useState({});
    const [userImage, setUserImage] = useState('');

    // Two-Factor Authentication states
    const [twoFactorStatus, setTwoFactorStatus] = useState({ enabled: false, verified_at: null });
    const [twoFactorLoading, setTwoFactorLoading] = useState(false);
    const [qrCodeData, setQrCodeData] = useState(null);
    const [verificationCode, setVerificationCode] = useState('');
    const [secretKey, setSecretKey] = useState('');
    const [showSetup, setShowSetup] = useState(false);
    const [showDisable, setShowDisable] = useState(false);

    // Form states
    const [name, setName] = useState('');
    const [email, setEmail] = useState('');
    const [currentPassword, setCurrentPassword] = useState('');
    const [newPassword, setNewPassword] = useState('');
    const [confirmPassword, setConfirmPassword] = useState('');
    
    // Additional user fields
    const [phone, setPhone] = useState('');
    const [position, setPosition] = useState('');
    const [customField1, setCustomField1] = useState('');
    const [customField2, setCustomField2] = useState('');
    const [customField3, setCustomField3] = useState('');
    const [customField4, setCustomField4] = useState('');
    const [customField5, setCustomField5] = useState('');
    const [customField6, setCustomField6] = useState('');
    const [customField7, setCustomField7] = useState('');
    const [customField8, setCustomField8] = useState('');
    const [customField9, setCustomField9] = useState('');
    const [customField10, setCustomField10] = useState('');

    // Password verification for profile update
    const [verificationPassword, setVerificationPassword] = useState('');
    const [showPasswordVerification, setShowPasswordVerification] = useState(false);
    const [passwordAttempts, setPasswordAttempts] = useState(0);
    const [isPasswordBlocked, setIsPasswordBlocked] = useState(false);

    // Email verification for profile update
    const [showEmailVerification, setShowEmailVerification] = useState(false);
    const [emailVerificationOtp, setEmailVerificationOtp] = useState(Array(6).fill(''));
    const [emailVerificationActiveInput, setEmailVerificationActiveInput] = useState(0);
    const [emailVerificationExpiry, setEmailVerificationExpiry] = useState(Date.now() + 600000); // 10 minutes
    const [emailVerificationTimeLeft, setEmailVerificationTimeLeft] = useState(600);
    const [emailVerificationExpired, setEmailVerificationExpired] = useState(false);
    const [emailVerificationResendCountdown, setEmailVerificationResendCountdown] = useState(0);
    const [isEmailVerificationResending, setIsEmailVerificationResending] = useState(false);
    const [pendingEmailUpdate, setPendingEmailUpdate] = useState(null);

    // Email verification input refs
    const emailVerificationInputRefs = useRef(Array.from({ length: 6 }, () => React.createRef()));

    // Email verification mutations
    const sendEmailVerification = useSendEmailVerificationForProfileMutation();
    const verifyEmailForProfile = useVerifyEmailForProfileMutation();
    const resendEmailVerification = useResendEmailVerificationForProfileMutation();

    // Email availability check states
    const [isEmailChecking, setIsEmailChecking] = useState(false);
    const [emailCheckTimeout, setEmailCheckTimeout] = useState(null);

    // Professional setup step
    const [setupStep, setSetupStep] = useState(1);
    
    // Phone country code state
    const [selectedCountry, setSelectedCountry] = useState({ name: 'Jordan', code: '+962', flag: '🇯🇴' });
    
    // Countries list
    const countries = [
        // Middle East & Arab Countries
        { name: 'Jordan', code: '+962', flag: '🇯🇴' },
        { name: 'Saudi Arabia', code: '+966', flag: '🇸🇦' },
        { name: 'UAE', code: '+971', flag: '🇦🇪' },
        { name: 'Kuwait', code: '+965', flag: '🇰🇼' },
        { name: 'Qatar', code: '+974', flag: '🇶🇦' },
        { name: 'Bahrain', code: '+973', flag: '🇧🇭' },
        { name: 'Oman', code: '+968', flag: '🇴🇲' },
        { name: 'Iraq', code: '+964', flag: '🇮🇶' },
        { name: 'Syria', code: '+963', flag: '🇸🇾' },
        { name: 'Lebanon', code: '+961', flag: '🇱🇧' },
        { name: 'Turkey', code: '+90', flag: '🇹🇷' },
        { name: 'Egypt', code: '+20', flag: '🇪🇬' },
        { name: 'Palestine', code: '+970', flag: '🇵🇸' },
        { name: 'Yemen', code: '+967', flag: '🇾🇪' },
        { name: 'Morocco', code: '+212', flag: '🇲🇦' },
        { name: 'Tunisia', code: '+216', flag: '🇹🇳' },
        { name: 'Algeria', code: '+213', flag: '🇩🇿' },
        
        // International
        { name: 'United States', code: '+1', flag: '🇺🇸' },
        { name: 'United Kingdom', code: '+44', flag: '🇬🇧' },
        { name: 'France', code: '+33', flag: '🇫🇷' },
        { name: 'Germany', code: '+49', flag: '🇩🇪' },
        { name: 'Canada', code: '+1', flag: '🇨🇦' },
        { name: 'Australia', code: '+61', flag: '🇦🇺' },
        { name: 'India', code: '+91', flag: '🇮🇳' },
        { name: 'Pakistan', code: '+92', flag: '🇵🇰' },
        { name: 'Bangladesh', code: '+880', flag: '🇧🇩' },
        { name: 'Malaysia', code: '+60', flag: '🇲🇾' },
        { name: 'Singapore', code: '+65', flag: '🇸🇬' },
        { name: 'Philippines', code: '+63', flag: '🇵🇭' },
        { name: 'Indonesia', code: '+62', flag: '🇮🇩' },
        { name: 'Thailand', code: '+66', flag: '🇹🇭' }
    ];
    
    // Remove qrScanned, isScanning, scanningError, videoRef, canvasRef
    // const [qrScanned, setQrScanned] = useState(false); // Removed
    // const [isScanning, setIsScanning] = useState(false); // Removed
    // const [scanningError, setScanningError] = useState(''); // Removed

    // Fetch user data on component mount
    useEffect(() => {
        fetchUserData();
        fetchTwoFactorStatus();
    }, []);  // eslint-disable-line react-hooks/exhaustive-deps

    // Email verification countdown timer
    useEffect(() => {
        let timer;
        if (emailVerificationResendCountdown > 0) {
            timer = setTimeout(() => setEmailVerificationResendCountdown(emailVerificationResendCountdown - 1), 1000);
        }
        return () => clearTimeout(timer);
    }, [emailVerificationResendCountdown]);

    // Email verification time left timer
    useEffect(() => {
        let timer;
        if (!emailVerificationExpired && emailVerificationTimeLeft > 0) {
            timer = setTimeout(() => {
                const newTimeLeft = Math.max(0, Math.floor((emailVerificationExpiry - Date.now()) / 1000));
                setEmailVerificationTimeLeft(newTimeLeft);
                if (newTimeLeft <= 0) {
                    setEmailVerificationExpired(true);
                }
            }, 1000);
        }
        return () => clearTimeout(timer);
    }, [emailVerificationExpiry, emailVerificationTimeLeft, emailVerificationExpired]);

    // Cleanup email check timeout on unmount
    useEffect(() => {
        return () => {
            if (emailCheckTimeout) {
                clearTimeout(emailCheckTimeout);
            }
        };
    }, [emailCheckTimeout]);

    // Phone formatting and validation functions
    const formatPhoneNumber = (value, countryCode) => {
        if (!value) return '';
        
        // Remove all non-numeric characters
        const cleaned = value.replace(/\D/g, '');
        
        // Apply formatting based on country
        switch (countryCode) {
            case '+1': // US/Canada format: (*************
                if (cleaned.length <= 3) return cleaned;
                if (cleaned.length <= 6) return `(${cleaned.slice(0, 3)}) ${cleaned.slice(3)}`;
                return `(${cleaned.slice(0, 3)}) ${cleaned.slice(3, 6)}-${cleaned.slice(6, 10)}`;
            
            case '+44': // UK format: 0123 456 7890
                if (cleaned.length <= 4) return cleaned;
                if (cleaned.length <= 7) return `${cleaned.slice(0, 4)} ${cleaned.slice(4)}`;
                return `${cleaned.slice(0, 4)} ${cleaned.slice(4, 7)} ${cleaned.slice(7, 11)}`;
            
            case '+962': // Jordan format: 07 9999 9999
            case '+963': // Syria
            case '+961': // Lebanon
                if (cleaned.length <= 2) return cleaned;
                if (cleaned.length <= 6) return `${cleaned.slice(0, 2)} ${cleaned.slice(2)}`;
                return `${cleaned.slice(0, 2)} ${cleaned.slice(2, 6)} ${cleaned.slice(6, 10)}`;
            
            case '+966': // Saudi Arabia: 5X XXX XXXX
            case '+971': // UAE: 5X XXX XXXX  
            case '+965': // Kuwait: 9XXX XXXX
            case '+974': // Qatar: 9XXX XXXX
            case '+973': // Bahrain: 9XXX XXXX
            case '+968': // Oman: 9XXX XXXX
                if (cleaned.length <= 2) return cleaned;
                if (cleaned.length <= 5) return `${cleaned.slice(0, 2)} ${cleaned.slice(2)}`;
                return `${cleaned.slice(0, 2)} ${cleaned.slice(2, 5)} ${cleaned.slice(5, 9)}`;
            
            case '+964': // Iraq: 07XX XXX XXXX
                if (cleaned.length <= 3) return cleaned;
                if (cleaned.length <= 6) return `${cleaned.slice(0, 3)} ${cleaned.slice(3)}`;
                return `${cleaned.slice(0, 3)} ${cleaned.slice(3, 6)} ${cleaned.slice(6, 10)}`;
            
            case '+90': // Turkey: (5XX) XXX XX XX
                if (cleaned.length <= 3) return cleaned;
                if (cleaned.length <= 6) return `(${cleaned.slice(0, 3)}) ${cleaned.slice(3)}`;
                if (cleaned.length <= 8) return `(${cleaned.slice(0, 3)}) ${cleaned.slice(3, 6)} ${cleaned.slice(6)}`;
                return `(${cleaned.slice(0, 3)}) ${cleaned.slice(3, 6)} ${cleaned.slice(6, 8)} ${cleaned.slice(8, 10)}`;
            
            default: // Generic formatting: XXX XXX XXXX
                if (cleaned.length <= 3) return cleaned;
                if (cleaned.length <= 6) return `${cleaned.slice(0, 3)} ${cleaned.slice(3)}`;
                return `${cleaned.slice(0, 3)} ${cleaned.slice(3, 6)} ${cleaned.slice(6)}`;
        }
    };

    const validatePhoneNumber = (phone, countryCode) => {
        if (!phone) return false;
        
        const cleaned = phone.replace(/\D/g, '');
        
        switch (countryCode) {
            case '+1': // US/Canada: 10 digits
                return cleaned.length === 10;
            case '+44': // UK: 10-11 digits
                return cleaned.length >= 10 && cleaned.length <= 11;
            case '+962': // Jordan: 9 digits
            case '+963': // Syria: 9 digits
            case '+961': // Lebanon: 8 digits
                return cleaned.length >= 8 && cleaned.length <= 9;
            case '+966': // Saudi Arabia: 9 digits
            case '+971': // UAE: 9 digits
            case '+965': // Kuwait: 8 digits
            case '+974': // Qatar: 8 digits
            case '+973': // Bahrain: 8 digits
            case '+968': // Oman: 8 digits
                return cleaned.length >= 8 && cleaned.length <= 9;
            case '+964': // Iraq: 10 digits
                return cleaned.length === 10;
            case '+90': // Turkey: 10 digits
                return cleaned.length === 10;
            default:
                return cleaned.length >= 7 && cleaned.length <= 15;
        }
    };

    const countryTemplate = (option) => {
        return (
            <div className="flex items-center">
                <span className="mr-2">{option.flag}</span>
                <span>{option.name} ({option.code})</span>
            </div>
        );
    };

    const selectedCountryTemplate = (option) => {
        if (option) {
            return (
                <div className="flex items-center">
                    <span className="mr-2">{option.flag}</span>
                    <span>{option.code}</span>
                </div>
            );
        }
        return <span>Select Country</span>;
    };

    // Validation functions
    const validateName = (name) => {
        if (!name || name.trim().length === 0) {
            return 'Name is required';
        }
        if (name.trim().length < 3) {
            return 'Name must be at least 3 characters long';
        }
        if (name.trim().length > 32) {
            return 'Name must not exceed 32 characters';
        }
        return null;
    };

    const validateEmail = (email) => {
        if (!email || email.trim().length === 0) {
            return 'Email is required';
        }
        const emailRegex = /^\w+([.-]?\w+)*@\w+([.-]?\w+)*(\.\w{2,3})+$/;
        if (!emailRegex.test(email)) {
            return 'Please enter a valid email address';
        }
        if (email.trim().length > 40) {
            return 'Email must not exceed 40 characters';
        }
        return null;
    };

    // Email availability check with debounce
    const checkEmailAvailability = async (email) => {
        // Clear previous timeout
        if (emailCheckTimeout) {
            clearTimeout(emailCheckTimeout);
        }

        // Check basic format first
        const formatError = validateEmail(email);
        if (formatError) {
            setErrors(prev => ({
                ...prev,
                email: formatError
            }));
            return;
        }

        // If email format is valid and contains @, check availability
        if (email && email.includes('@')) {
            setIsEmailChecking(true);
            
            // Set new timeout for debounced check
            const timeout = setTimeout(async () => {
                try {
                    const userId = localStorage.getItem('user_id');
                    const result = await userAPI.checkEmailAvailability(email, userId);
                    
                    if (result.success) {
                        if (!result.available) {
                            setErrors(prev => ({
                                ...prev,
                                email: 'This email address is already taken by another user'
                            }));
                        } else {
                            // Clear email error if available
                            setErrors(prev => {
                                const newErrors = { ...prev };
                                delete newErrors.email;
                                return newErrors;
                            });
                        }
                    } else {
                        setErrors(prev => ({
                            ...prev,
                            email: 'Failed to check email availability'
                        }));
                    }
                } catch (error) {
                    setErrors(prev => ({
                        ...prev,
                        email: 'Failed to check email availability'
                    }));
                } finally {
                    setIsEmailChecking(false);
                }
            }, 500); // 500ms delay

            setEmailCheckTimeout(timeout);
        } else {
            setIsEmailChecking(false);
        }
    };

    const validatePhone = (phone) => {
        if (!phone || phone.trim().length === 0) {
            return 'Phone number is required';
        }
        if (!validatePhoneNumber(phone, selectedCountry.code)) {
            return 'Invalid phone number format for selected country';
        }
        return null;
    };

    const validatePosition = (position) => {
        if (!position || position.trim().length === 0) {
            return 'Position is required';
        }
        if (position.trim().length < 2) {
            return 'Position must be at least 2 characters long';
        }
        if (position.trim().length > 50) {
            return 'Position must not exceed 50 characters';
        }
        // Check if position contains only numbers
        if (/^\d+$/.test(position.trim())) {
            return 'Position cannot contain only numbers';
        }
        // Check if position contains at least one letter
        if (!/[a-zA-Z]/.test(position.trim())) {
            return 'Position must contain at least one letter';
        }
        return null;
    };



    const validateCustomField = (value, fieldName) => {
        if (value && value.trim().length > 0) {
            if (value.trim().length < 3) {
                return `${fieldName} must be at least 3 characters long`;
            }
            if (value.trim().length > 32) {
                return `${fieldName} must not exceed 32 characters`;
            }
        }
        return null;
    };

    // Remove all useEffect and functions related to camera/jsQR/scanQRCode
    // const startQRScanning = async () => { ... }; // Removed
    // const scanQRCode = () => { ... }; // Removed
    // const stopQRScanning = () => { ... }; // Removed

    const fetchUserData = async () => {
        try {
            const userId = localStorage.getItem('user_id');
            const response = await axiosInstance.get(`/users/${userId}`);
            const userData = response.data.data || response.data;

            setName(userData.name || '');
            setEmail(userData.email || '');
            setOriginalName(userData.name || '');
            setOriginalEmail(userData.email || '');
            
            // Set additional fields
            setPhone(userData.phone || '');
            
            // Set country code based on phone number
            if (userData.phone) {
                const phoneNumber = userData.phone;
                const country = countries.find(c => phoneNumber.startsWith(c.code));
                if (country) {
                    setSelectedCountry(country);
                }
            }
            
            setPosition(userData.position || '');
            setCustomField1(userData.custom_field_1 || '');
            setCustomField2(userData.custom_field_2 || '');
            setCustomField3(userData.custom_field_3 || '');
            setCustomField4(userData.custom_field_4 || '');
            setCustomField5(userData.custom_field_5 || '');
            setCustomField6(userData.custom_field_6 || '');
            setCustomField7(userData.custom_field_7 || '');
            setCustomField8(userData.custom_field_8 || '');
            setCustomField9(userData.custom_field_9 || '');
            setCustomField10(userData.custom_field_10 || '');
            
            if (userData.image) {
                setUserImage(userData.image);
                localStorage.setItem('user_image', userData.image);
            } else {
                setUserImage('');
                localStorage.removeItem('user_image');
            }
        } catch (error) {
            console.error('Error fetching user data:', error);
            showMessage('error', 'Error', 'Failed to load user data');
        }
    };

    const fetchTwoFactorStatus = async () => {
        try {
            const userId = localStorage.getItem('user_id');
            const token = localStorage.getItem('token');
            
            console.log('🔍 Fetching 2FA status for user:', userId);

            const response = await axiosInstance.get('/two-factor/status', {
                params: { user_id: userId },
                headers: token ? {
                    'Authorization': `Bearer ${token}`
                } : {}
            });
            
            console.log('✅ 2FA Status:', response.data);
            setTwoFactorStatus(response.data);
        } catch (error) {
            console.error('❌ Error fetching two-factor status:', error);
            console.error('❌ Error response:', error.response?.data);
        }
    };

    const showMessage = (severity, summary, detail) => {
        toast.current?.show({ severity, summary, detail, life: 3000 });
    };

    const showDetailedError = (error, defaultMessage = 'An error occurred') => {
        const errorMessage = error?.response?.data?.message || defaultMessage;
        const errorDetails = error?.response?.data?.details;
        const errorErrors = error?.response?.data?.errors;
        
        // Show main error message
        showMessage('error', 'Error', errorMessage);
        
        // Log detailed error for debugging
        console.error('Detailed error information:', {
            message: error?.response?.data?.message,
            details: errorDetails,
            errors: errorErrors,
            status: error?.response?.status,
            statusText: error?.response?.statusText
        });
        
        // If there are field-specific errors, show them
        if (errorErrors && typeof errorErrors === 'object') {
            Object.keys(errorErrors).forEach(field => {
                const fieldErrors = errorErrors[field];
                if (Array.isArray(fieldErrors) && fieldErrors.length > 0) {
                    showMessage('error', `${field} Error`, fieldErrors[0]);
                }
            });
        }
        
        // If there are detailed errors, show them
        if (errorDetails && typeof errorDetails === 'object') {
            Object.keys(errorDetails).forEach(field => {
                const fieldErrors = errorDetails[field];
                if (Array.isArray(fieldErrors) && fieldErrors.length > 0) {
                    showMessage('error', `${field} Error`, fieldErrors[0]);
                }
            });
        }
    };

    // Helper function to clear field error
    const clearFieldError = (fieldName) => {
        setErrors(prev => {
            const newErrors = { ...prev };
            delete newErrors[fieldName];
            return newErrors;
        });
    };

    // Helper function to check if form is valid
    const isFormValid = () => {
        return (
            !loading &&
            !isEmailChecking &&
            Object.keys(errors).length === 0 &&
            name.trim() &&
            email.trim() &&
            phone.trim() &&
            position.trim()
        );
    };

    // Email verification functions
    const handleEmailVerificationOtpChange = (index, value) => {
        const newOtp = [...emailVerificationOtp];
        newOtp[index] = value;
        setEmailVerificationOtp(newOtp);
        
        if (value && index < 5) {
            setEmailVerificationActiveInput(index + 1);
            emailVerificationInputRefs.current[index + 1]?.current?.focus();
        }
    };

    const handleEmailVerificationOtpKeyDown = (index, e) => {
        if (e.key === 'Backspace' && !emailVerificationOtp[index] && index > 0) {
            setEmailVerificationActiveInput(index - 1);
            emailVerificationInputRefs.current[index - 1]?.current?.focus();
        }
    };

    const handleEmailVerificationOtpPaste = (e) => {
        e.preventDefault();
        const pastedData = e.clipboardData.getData('text/plain').replace(/\D/g, '').slice(0, 6);
        if (pastedData.length === 6) {
            setEmailVerificationOtp(pastedData.split(''));
            setEmailVerificationActiveInput(5);
            emailVerificationInputRefs.current[5]?.current?.focus();
        }
    };

    const formatEmailVerificationTime = (seconds) => {
        const minutes = Math.floor(seconds / 60);
        const remainingSeconds = seconds % 60;
        return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
    };

    const handleEmailVerificationResend = async () => {
        if (!pendingEmailUpdate) return;
        
        setIsEmailVerificationResending(true);
        try {
            await resendEmailVerification.mutateAsync({
                email: pendingEmailUpdate,
                user_id: localStorage.getItem('user_id')
            });
            setEmailVerificationResendCountdown(60);
            setEmailVerificationExpiry(Date.now() + 600000);
            setEmailVerificationTimeLeft(600);
            setEmailVerificationExpired(false);
            setEmailVerificationOtp(Array(6).fill(''));
            setEmailVerificationActiveInput(0);
            emailVerificationInputRefs.current[0]?.current?.focus();
        } catch (error) {
            // Show detailed error message from API
            showDetailedError(error, 'Failed to resend verification email');
        } finally {
            setIsEmailVerificationResending(false);
        }
    };

    const handleEmailVerificationSubmit = async () => {
        if (emailVerificationExpired) {
            showMessage('error', 'Code Expired', 'Verification code has expired. Please resend.');
            return;
        }
        
        if (emailVerificationOtp.some((d) => !d)) {
            showMessage('error', 'Incomplete Code', 'Please enter the full 6-digit verification code.');
            return;
        }

        try {
            await verifyEmailForProfile.mutateAsync({
                email: pendingEmailUpdate,
                otp: emailVerificationOtp.join(''),
                user_id: localStorage.getItem('user_id')
            });

            // Email verified successfully - now proceed with final profile update
            // The email will be updated in the backend during the final update
            showMessage('success', 'Email Verified', 'Email verification completed successfully!');
            setShowEmailVerification(false);
            setEmailVerificationOtp(Array(6).fill(''));
            setEmailVerificationActiveInput(0);
            
            // Now proceed with the final profile update
            await handleFinalProfileUpdate();
        } catch (error) {
            // Show detailed error message from API
            showDetailedError(error, 'Failed to verify email');
        }
    };

    const handleFinalProfileUpdate = async () => {
        setLoading(true);
        setErrors({});

        try {
            const userId = localStorage.getItem('user_id');

            // Determine which email to send based on verification status
            // This ensures email is only updated if it was properly verified
            let emailToUpdate = email;
            
            if (email !== originalEmail && pendingEmailUpdate) {
                // Email was changed and verified, use the new email
                emailToUpdate = pendingEmailUpdate;
            } else if (email !== originalEmail && !pendingEmailUpdate) {
                // Email was changed but not verified, keep the original email
                emailToUpdate = originalEmail;
                showMessage('warn', 'Email Not Verified', 'Email change was not verified. Profile updated with original email.');
            }

            console.log('Sending final profile update data:', {
                name: name,
                email: emailToUpdate,
                current_password: verificationPassword
            });

            const response = await axiosInstance.put(`/users/${userId}`, {
                name: name,
                email: emailToUpdate,
                current_password: verificationPassword,
                phone: phone,
                position: position,
                custom_field_1: customField1,
                custom_field_2: customField2,
                custom_field_3: customField3,
                custom_field_4: customField4,
                custom_field_5: customField5,
                custom_field_6: customField6,
                custom_field_7: customField7,
                custom_field_8: customField8,
                custom_field_9: customField9,
                custom_field_10: customField10
            });

            console.log('Profile update response:', response.data);

            showMessage('success', 'Profile Updated', 'All changes saved successfully!');
            setIsEditingProfile(false);
            setShowPasswordVerification(false);
            setVerificationPassword('');
            setPasswordAttempts(0);
            setIsPasswordBlocked(false);
            setOriginalName(name);
            setOriginalEmail(emailToUpdate);
            
            // Update the form email to match what was actually saved
            setEmail(emailToUpdate);
            
            // Clear email verification states
            setPendingEmailUpdate(null);
            setShowEmailVerification(false);
            setEmailVerificationOtp(Array(6).fill(''));
            setEmailVerificationActiveInput(0);
        } catch (error) {
            console.error('Error updating profile:', error);
            console.log('Error response:', error.response?.data);

            if (error.response?.data?.details) {
                setErrors(error.response.data.details);
            } else if (error.response?.data?.errors) {
                setErrors(error.response.data.errors);
            }

            showDetailedError(error, 'Failed to update profile');
            
            // Handle password verification failures with rate limiting
            const errorMessage = error.response?.data?.message || '';
            if (errorMessage.includes('password') || errorMessage.includes('incorrect')) {
                const newAttempts = passwordAttempts + 1;
                setPasswordAttempts(newAttempts);
                setVerificationPassword('');
                
                if (newAttempts >= 3) {
                    setIsPasswordBlocked(true);
                    setShowPasswordVerification(false);
                    showMessage('warn', 'Account Temporarily Locked', 
                        'Too many incorrect password attempts. Please wait 5 minutes before trying again.');
                    
                    // Auto-unlock after 5 minutes
                    setTimeout(() => {
                        setIsPasswordBlocked(false);
                        setPasswordAttempts(0);
                        showMessage('info', 'Account Unlocked', 'You can now try updating your profile again.');
                    }, 300000); // 5 minutes
                } else {
                    showMessage('warn', 'Incorrect Password', 
                        `${3 - newAttempts} attempts remaining before temporary lockout.`);
                }
            }
        } finally {
            setLoading(false);
        }
    };

    const handleImageUpload = async (event) => {
        const file = event.target.files[0];
        if (!file) return;

        const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif'];
        if (!allowedTypes.includes(file.type)) {
            showMessage('error', 'Invalid File Type', 'Please select a valid image file (JPEG, PNG, GIF)');
            return;
        }

        const maxSize = 5 * 1024 * 1024;
        if (file.size > maxSize) {
            showMessage('error', 'File Too Large', 'Image size should be less than 5MB');
            return;
        }

        setImageLoading(true);
        setErrors({});

        try {
            const userId = localStorage.getItem('user_id');
            const formData = new FormData();
            formData.append('image', file);

            const response = await axiosInstance.post(`/users/${userId}/upload-image`, formData, {
                headers: {
                    'Content-Type': 'multipart/form-data',
                },
            });

            console.log('Image upload response:', response.data);

            const newImageUrl = response.data.data?.image || response.data.image;
            setUserImage(newImageUrl);
            localStorage.setItem('user_image', newImageUrl);
            
            showMessage('success', 'Image Updated', 'Profile image updated successfully');
            await fetchUserData();

        } catch (error) {
            console.error('Error uploading image:', error);
            console.log('Error response:', error.response?.data);

            if (error.response?.data?.details) {
                setErrors(error.response.data.details);
            } else if (error.response?.data?.errors) {
                setErrors(error.response.data.errors);
            }

            showMessage('error', 'Upload Failed', error.response?.data?.message || 'Failed to upload image');
        } finally {
            setImageLoading(false);
        }
    };

    const handleUpdateProfile = async () => {
        // Check if there are any validation errors
        if (Object.keys(errors).length > 0) {
            showMessage('error', 'Validation Errors', 'Please fix all validation errors before saving changes.');
            return;
        }

        // Check if email is being validated
        if (isEmailChecking) {
            showMessage('warn', 'Please Wait', 'Email validation is in progress. Please wait.');
            return;
        }

        // Check if required fields are empty
        if (!name.trim() || !email.trim() || !phone.trim() || !position.trim()) {
            showMessage('error', 'Required Fields', 'Please fill in all required fields.');
            return;
        }

        // Check if account is blocked
        if (isPasswordBlocked) {
            showMessage('warn', 'Account Locked', 'Please wait for the lockout period to expire before trying again.');
            return;
        }

        // Step 1: Password verification first
        if (!showPasswordVerification) {
            setShowPasswordVerification(true);
            return;
        }

        if (!verificationPassword) {
            showMessage('error', 'Password Required', 'Please enter your current password to verify changes');
            setErrors({ verificationPassword: 'Current password is required' });
            return;
        }

        // Step 2: Email verification (only if email changed and password is verified)
        // This ensures email is only changed after both password and email verification

        // Step 2: Email verification (only if email changed and password is verified)
        if (email !== originalEmail && !showEmailVerification) {
            setPendingEmailUpdate(email);
            setShowEmailVerification(true);
            
            // Send verification email
            try {
                await sendEmailVerification.mutateAsync({
                    email: email,
                    user_id: localStorage.getItem('user_id')
                });
                setEmailVerificationResendCountdown(60);
                setEmailVerificationExpiry(Date.now() + 600000);
                setEmailVerificationTimeLeft(600);
                setEmailVerificationExpired(false);
                setEmailVerificationOtp(Array(6).fill(''));
                setEmailVerificationActiveInput(0);
                emailVerificationInputRefs.current[0]?.current?.focus();
                return;
        } catch (error) {
                // If email verification fails, revert email and show detailed error
                setEmail(originalEmail);
                setPendingEmailUpdate(null);
                showDetailedError(error, 'Failed to send verification email. Please try again.');
                return;
            }
        }

        // If we reach here, it means password is verified
        // If email was changed, we need to wait for email verification
        if (email !== originalEmail && !pendingEmailUpdate) {
            showMessage('warn', 'Email Verification Required', 'Please complete email verification before saving changes. Your email will not be updated until verified.');
            return;
        }

        // If we reach here, it means password is verified and email is either unchanged or verified
        // Proceed with final profile update
        await handleFinalProfileUpdate();
    };

    const handleChangePassword = async () => {
        if (newPassword !== confirmPassword) {
            showMessage('error', 'Error', 'Passwords do not match');
            setErrors({ confirmPassword: 'Passwords do not match' });
            return;
        }

        setLoading(true);
        setErrors({});

        try {
            const userId = localStorage.getItem('user_id');

            const passwordData = {
                id: userId,
                current_password: currentPassword,
                new_password: newPassword,
                new_password_confirmation: confirmPassword
            };

            const response = await axiosInstance.post(`/users/change-password`, passwordData);

            console.log('Password change response:', response.data);

            showMessage('success', 'Password Changed', 'Password updated successfully');
            setIsEditingPassword(false);
            setCurrentPassword('');
            setNewPassword('');
            setConfirmPassword('');
        } catch (error) {
            console.error('Error changing password:', error);
            console.log('Error response:', error.response?.data);

            if (error.response?.data?.details) {
                setErrors(error.response.data.details);
            } else if (error.response?.data?.errors) {
                setErrors(error.response.data.errors);
            }

            showMessage('error', 'Update Failed', error.response?.data?.message || 'Failed to update password');
        } finally {
            setLoading(false);
        }
    };

    const handleTwoFactorSetup = async () => {
        setTwoFactorLoading(true);
        try {
            // Get user authentication data
            const userId = localStorage.getItem('user_id');
            const token = localStorage.getItem('token');
            
            console.log('🔍 Starting 2FA setup for user:', userId);

            const response = await axiosInstance.post('/two-factor/setup', {
                user_id: userId
            }, {
                headers: token ? {
                    'Authorization': `Bearer ${token}`
                } : {}
            });
            
            console.log('✅ QR Code Setup Response:', response.data);
            console.log('📱 QR URL:', response.data.qr_url);
            console.log('🔑 Secret Key:', response.data.secret);
            
            setQrCodeData(response.data);
            setSecretKey(response.data.secret);
            setShowSetup(true);
            setSetupStep(1); // Start with step 1 (QR code display)
        } catch (error) {
            console.error('❌ Error setting up two-factor:', error);
            console.error('❌ Error response:', error.response?.data);
            console.error('❌ Error status:', error.response?.status);
            
            const errorMessage = error.response?.data?.message || 'Failed to setup two-factor authentication';
            console.log('🔍 Setup failed with message:', errorMessage);
            
            showMessage('error', 'Setup Failed', errorMessage);
        } finally {
            setTwoFactorLoading(false);
        }
    };

    const handleTwoFactorVerify = async () => {
        if (!verificationCode || verificationCode.length !== 6) {
            showMessage('error', 'Invalid Code', 'Please enter a valid 6-digit code');
            return;
        }

        setTwoFactorLoading(true);
        try {
            // Get user authentication data
            const userId = localStorage.getItem('user_id');
            const token = localStorage.getItem('token');
            
            console.log('🔍 Verifying 2FA setup:', {
                code: verificationCode,
                userId: userId,
                hasToken: !!token
            });

            await axiosInstance.post('/two-factor/verify', {
                code: verificationCode,
                user_id: userId
            }, {
                headers: token ? {
                    'Authorization': `Bearer ${token}`
                } : {}
            });
            
            console.log('✅ 2FA setup verification successful!');
            showMessage('success', 'Success', 'Two-factor authentication enabled successfully');
            setShowSetup(false);
            setVerificationCode('');
            setQrCodeData(null);
            setSecretKey('');
            // setQrScanned(false); // Removed
            // stopQRScanning(); // Removed
            await fetchTwoFactorStatus();
            setSetupStep(3); // Automatically progress to step 3
        } catch (error) {
            console.error('❌ Error verifying two-factor:', error);
            console.error('❌ Error response:', error.response?.data);
            console.error('❌ Error status:', error.response?.status);
            
            const errorMessage = error.response?.data?.message || 'Invalid verification code';
            console.log('🔍 Setup verification failed with message:', errorMessage);
            
            showMessage('error', 'Verification Failed', errorMessage);
        } finally {
            setTwoFactorLoading(false);
        }
    };

    const handleTwoFactorDisable = async () => {
        if (!verificationCode || verificationCode.length !== 6) {
            showMessage('error', 'Invalid Code', 'Please enter a valid 6-digit code');
            return;
        }

        setTwoFactorLoading(true);
        try {
            // Get user authentication data
            const userId = localStorage.getItem('user_id');
            const token = localStorage.getItem('token');
            
            console.log('🔍 Disabling 2FA:', {
                code: verificationCode,
                userId: userId,
                hasToken: !!token
            });

            await axiosInstance.post('/two-factor/disable', {
                code: verificationCode,
                user_id: userId
            }, {
                headers: token ? {
                    'Authorization': `Bearer ${token}`
                } : {}
            });
            
            console.log('✅ 2FA disabled successfully!');
            showMessage('success', 'Success', 'Two-factor authentication disabled successfully');
            setShowDisable(false);
            setVerificationCode('');
            await fetchTwoFactorStatus();
        } catch (error) {
            console.error('❌ Error disabling two-factor:', error);
            console.error('❌ Error response:', error.response?.data);
            console.error('❌ Error status:', error.response?.status);
            
            const errorMessage = error.response?.data?.message || 'Invalid verification code';
            console.log('🔍 Disable verification failed with message:', errorMessage);
            
            showMessage('error', 'Disable Failed', errorMessage);
        } finally {
            setTwoFactorLoading(false);
        }
    };

    const enterEditProfileMode = () => {
        setOriginalName(name);
        setOriginalEmail(email);
        setIsEditingProfile(true);
    };

    const cancelEditProfile = () => {
        setName(originalName);
        setEmail(originalEmail);
        setIsEditingProfile(false);
        setShowPasswordVerification(false);
        setShowEmailVerification(false);
        setVerificationPassword('');
        setPasswordAttempts(0);
        setIsPasswordBlocked(false);
        setPendingEmailUpdate(null);
        setEmailVerificationOtp(Array(6).fill(''));
        setEmailVerificationActiveInput(0);
        setErrors({});
        
        // Reset additional fields to original values
        fetchUserData();
    };

    const enterEditImageMode = () => {
        setTimeout(() => {
            fileInputRef.current?.click();
        }, 100);
    };

    const renderTwoFactorSection = () => (
        <div className="bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 rounded-3xl p-8 border border-white/20 shadow-2xl backdrop-blur-xl relative">
            {/* Background Pattern */}
            <div className="absolute inset-0 opacity-30">
                <div className="w-full h-full" style={{
                    backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.03'%3E%3Ccircle cx='30' cy='30' r='2'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`
                }}></div>
            </div>
            
            {/* Animated Background Elements */}
            <div className="absolute top-0 left-0 w-full h-full overflow-hidden pointer-events-none">
                <div className="absolute -top-20 -right-20 w-40 h-40 bg-gradient-to-br from-blue-500/10 to-purple-600/10 rounded-full blur-2xl animate-pulse"></div>
                <div className="absolute -bottom-20 -left-20 w-40 h-40 bg-gradient-to-tr from-purple-500/10 to-pink-600/10 rounded-full blur-2xl animate-pulse delay-1000"></div>
            </div>

            <div className="relative z-10">
                <div className="flex items-center gap-4 mb-8">
                    <div className="w-16 h-16 bg-gradient-to-br from-blue-500 via-purple-500 to-pink-500 rounded-full flex items-center justify-center shadow-2xl relative overflow-hidden">
                        <div className="absolute inset-0 bg-gradient-to-br from-white/20 to-transparent"></div>
                        <i className="pi pi-shield text-white text-2xl relative z-10"></i>
                        <div className="absolute inset-0 bg-gradient-to-br from-transparent to-black/20"></div>
                    </div>
                    <div>
                        <h3 className="text-2xl font-bold text-white mb-2">Two-Factor Authentication</h3>
                        <p className="text-gray-300">Secure your account with an additional layer of protection</p>
                    </div>
                </div>

                {!showSetup && !showDisable && (
                    <div className="grid lg:grid-cols-2 gap-8">
                        {/* Left Column - Status & Actions */}
                        <div className="space-y-6">
                            {/* Status Card */}
                            <div className={`rounded-2xl p-6 border ${
                                twoFactorStatus.enabled 
                                    ? 'bg-gradient-to-r from-emerald-500/20 to-blue-500/20 border-emerald-400/30' 
                                    : 'bg-gradient-to-r from-amber-500/20 to-orange-500/20 border-amber-400/30'
                            }`}>
                                <div className="flex items-center gap-4">
                                    <div className={`w-12 h-12 rounded-full flex items-center justify-center ${
                                        twoFactorStatus.enabled 
                                            ? 'bg-emerald-500/20' 
                                            : 'bg-amber-500/20'
                                    }`}>
                                        <i className={`text-xl ${
                                            twoFactorStatus.enabled 
                                                ? 'pi pi-check-circle text-emerald-400' 
                                                : 'pi pi-exclamation-triangle text-amber-400'
                                        }`}></i>
                                    </div>
                                    <div>
                                        <h4 className={`text-lg font-semibold mb-1 ${
                                            twoFactorStatus.enabled ? 'text-white' : 'text-white'
                                        }`}>
                                            {twoFactorStatus.enabled ? '2FA is Enabled' : '2FA is Disabled'}
                                        </h4>
                                        <p className={`text-sm ${
                                            twoFactorStatus.enabled ? 'text-emerald-300' : 'text-amber-300'
                                        }`}>
                                            {twoFactorStatus.enabled 
                                                ? 'Your account is protected with two-factor authentication'
                                                : 'Enable 2FA to add an extra layer of security to your account'
                                            }
                                        </p>
                                    </div>
                                </div>
                            </div>

                            {/* Action Buttons */}
                            <div className="space-y-4">
                                {!twoFactorStatus.enabled ? (
                                    <Button
                                        label="Enable Two-Factor Authentication"
                                        icon="pi pi-shield"
                                        className="w-full py-4 text-lg font-semibold bg-gradient-to-r from-blue-600 via-purple-600 to-pink-600 hover:from-blue-700 hover:via-purple-700 hover:to-pink-700 hover:scale-105 active:scale-95 border-0 shadow-2xl rounded-2xl transition-all duration-300 text-white"
                                        onClick={handleTwoFactorSetup}
                                        loading={twoFactorLoading}
                                    />
                                ) : (
                                    <Button
                                        label="Disable Two-Factor Authentication"
                                        icon="pi pi-lock-open"
                                        severity="danger"
                                        className="w-full py-4 text-lg font-semibold bg-gradient-to-r from-red-600 via-orange-600 to-pink-600 hover:from-red-700 hover:via-orange-700 hover:to-pink-700 hover:scale-105 active:scale-95 border-0 shadow-2xl rounded-2xl transition-all duration-300 text-white"
                                        onClick={() => setShowDisable(true)}
                                        loading={twoFactorLoading}
                                    />
                                )}
                            </div>

                            {/* Security Features */}
                            <div className="bg-gradient-to-r from-emerald-500/20 to-blue-500/20 rounded-2xl p-6 border border-emerald-400/30">
                                <div className="flex items-center gap-4 mb-4">
                                    <i className="pi pi-shield text-emerald-400 text-2xl"></i>
                                    <h4 className="text-lg font-bold text-white">Security Features</h4>
                                </div>
                                <div className="grid grid-cols-2 gap-3 text-sm">
                                    <div className="flex items-center gap-2 text-emerald-300">
                                        <i className="pi pi-check-circle"></i>
                                        <span>30-second codes</span>
                                    </div>
                                    <div className="flex items-center gap-2 text-emerald-300">
                                        <i className="pi pi-check-circle"></i>
                                        <span>3 attempt limit</span>
                                    </div>
                                    <div className="flex items-center gap-2 text-emerald-300">
                                        <i className="pi pi-check-circle"></i>
                                        <span>Auto-lock protection</span>
                                    </div>
                                    <div className="flex items-center gap-2 text-emerald-300">
                                        <i className="pi pi-check-circle"></i>
                                        <span>Secure encryption</span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        {/* Right Column - Information */}
                        <div className="space-y-6">
                            {/* How it Works */}
                            <div className="bg-white/10 backdrop-blur-xl rounded-2xl p-6 border border-white/20">
                                <div className="flex items-start gap-4 mb-6">
                                    <div className="w-12 h-12 bg-blue-500/20 rounded-full flex items-center justify-center flex-shrink-0">
                                        <i className="pi pi-info-circle text-blue-400 text-xl"></i>
                                    </div>
                                    <div>
                                        <h4 className="text-xl font-bold text-white mb-2">How 2FA Works</h4>
                                        <p className="text-gray-300 text-sm leading-relaxed">
                                            Two-factor authentication adds an extra layer of security by requiring a second form of verification in addition to your password.
                                        </p>
                                    </div>
                                </div>
                                
                                <div className="space-y-3">
                                    <div className="flex items-center gap-3 p-3 bg-white/5 rounded-xl">
                                        <div className="w-6 h-6 bg-blue-500 rounded-full flex items-center justify-center text-white font-bold text-xs">1</div>
                                        <span className="text-white text-sm">Scan QR code with authenticator app</span>
                                    </div>
                                    <div className="flex items-center gap-3 p-3 bg-white/5 rounded-xl">
                                        <div className="w-6 h-6 bg-purple-500 rounded-full flex items-center justify-center text-white font-bold text-xs">2</div>
                                        <span className="text-white text-sm">Enter 6-digit code to verify setup</span>
                                    </div>
                                    <div className="flex items-center gap-3 p-3 bg-white/5 rounded-xl">
                                        <div className="w-6 h-6 bg-pink-500 rounded-full flex items-center justify-center text-white font-bold text-xs">3</div>
                                        <span className="text-white text-sm">Use app codes for future logins</span>
                                    </div>
                                </div>
                            </div>

                            {/* Supported Apps */}
                            <div className="bg-white/10 backdrop-blur-xl rounded-2xl p-6 border border-white/20">
                                <div className="flex items-start gap-4 mb-4">
                                    <div className="w-12 h-12 bg-purple-500/20 rounded-full flex items-center justify-center flex-shrink-0">
                                        <i className="pi pi-mobile text-purple-400 text-xl"></i>
                                    </div>
                                    <div>
                                        <h4 className="text-lg font-bold text-white mb-2">Supported Apps</h4>
                                        <p className="text-gray-300 text-sm">Use any TOTP-compatible authenticator app</p>
                                    </div>
                                </div>
                                
                                <div className="grid grid-cols-2 gap-3 text-sm">
                                    <div className="flex items-center gap-2 text-purple-300">
                                        <i className="pi pi-check-circle"></i>
                                        <span>Google Authenticator</span>
                                    </div>
                                    <div className="flex items-center gap-2 text-purple-300">
                                        <i className="pi pi-check-circle"></i>
                                        <span>Authy</span>
                                    </div>
                                    <div className="flex items-center gap-2 text-purple-300">
                                        <i className="pi pi-check-circle"></i>
                                        <span>Microsoft Authenticator</span>
                                    </div>
                                    <div className="flex items-center gap-2 text-purple-300">
                                        <i className="pi pi-check-circle"></i>
                                        <span>1Password</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                )}

                {/* Professional Step-by-Step Setup */}
                {showSetup && (
                    <div className="space-y-8">
                        {/* Step Progress Indicator */}
                        <div className="bg-white/10 backdrop-blur-xl rounded-2xl p-6 border border-white/20">
                            <div className="flex items-center justify-between mb-6">
                                <h3 className="text-xl font-bold text-white">Setup Progress</h3>
                                <div className="text-sm text-gray-300">
                                    Step {setupStep} of 3
                                </div>
                            </div>
                            
                            {/* Progress Bar */}
                            <div className="w-full bg-white/10 rounded-full h-3 mb-6">
                                <div 
                                    className="bg-gradient-to-r from-blue-500 via-purple-500 to-pink-500 h-3 rounded-full transition-all duration-500 ease-out"
                                    style={{ width: `${(setupStep / 3) * 100}%` }}
                                ></div>
                            </div>
                            
                            {/* Step Indicators */}
                            <div className="flex items-center justify-between">
                                <div className={`flex items-center gap-3 ${setupStep >= 1 ? 'text-blue-400' : 'text-gray-500'}`}>
                                    <div className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-bold ${
                                        setupStep >= 1 
                                            ? 'bg-blue-500 text-white' 
                                            : 'bg-white/10 text-gray-400'
                                    }`}>
                                        {setupStep > 1 ? <i className="pi pi-check text-xs"></i> : '1'}
                                    </div>
                                    <span className="text-sm font-medium">QR Code Scan</span>
                                </div>
                                
                                <div className={`flex-1 h-0.5 mx-4 ${
                                    setupStep >= 2 ? 'bg-blue-500' : 'bg-white/10'
                                }`}></div>
                                
                                <div className={`flex items-center gap-3 ${setupStep >= 2 ? 'text-purple-400' : 'text-gray-500'}`}>
                                    <div className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-bold ${
                                        setupStep >= 2 
                                            ? 'bg-purple-500 text-white' 
                                            : 'bg-white/10 text-gray-400'
                                    }`}>
                                        {setupStep > 2 ? <i className="pi pi-check text-xs"></i> : '2'}
                                    </div>
                                    <span className="text-sm font-medium">Verification</span>
                                </div>
                                
                                <div className={`flex-1 h-0.5 mx-4 ${
                                    setupStep >= 3 ? 'bg-purple-500' : 'bg-white/10'
                                }`}></div>
                                
                                <div className={`flex items-center gap-3 ${setupStep >= 3 ? 'text-pink-400' : 'text-gray-500'}`}>
                                    <div className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-bold ${
                                        setupStep >= 3 
                                            ? 'bg-pink-500 text-white' 
                                            : 'bg-white/10 text-gray-400'
                                    }`}>
                                        {setupStep > 3 ? <i className="pi pi-check text-xs"></i> : '3'}
                                    </div>
                                    <span className="text-sm font-medium">Complete</span>
                                </div>
                            </div>
                        </div>

                        {/* Step 1: QR Code Scan */}
                        {setupStep === 1 && (
                            <div className="space-y-6">
                        <div className="bg-gradient-to-r from-blue-500/20 to-purple-500/20 border border-blue-400/30 rounded-2xl p-6">
                            <div className="flex items-start gap-4">
                                <div className="w-12 h-12 bg-blue-500/20 rounded-full flex items-center justify-center flex-shrink-0">
                                            <i className="pi pi-qrcode text-blue-400 text-xl"></i>
                                </div>
                                <div className="flex-1">
                                            <h3 className="text-xl font-semibold text-white mb-3">Step 1: Scan QR Code</h3>
                                    <div className="space-y-3">
                                        <div className="flex items-center gap-3 p-3 bg-white/10 rounded-xl">
                                            <div className="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center text-white font-bold text-sm">1</div>
                                            <span className="text-white">Open your authenticator app (Google Authenticator, Authy, etc.)</span>
                                        </div>
                                        <div className="flex items-center gap-3 p-3 bg-white/10 rounded-xl">
                                            <div className="w-8 h-8 bg-purple-500 rounded-full flex items-center justify-center text-white font-bold text-sm">2</div>
                                            <span className="text-white">Scan the QR code below with your app</span>
                                        </div>
                                        <div className="flex items-center gap-3 p-3 bg-white/10 rounded-xl">
                                            <div className="w-8 h-8 bg-pink-500 rounded-full flex items-center justify-center text-white font-bold text-sm">3</div>
                                            <span className="text-white">
                                                {/* Removed QR code scanning indicators */}
                                                Point your camera at the QR code below
                                            </span>
                                        </div>
                                        
                                        {/* Removed Camera scanning indicator */}
                                        
                                        {/* Removed Scanning error */}
                                        
                                        {/* Hidden video and canvas elements for QR scanning */}
                                        <div className="hidden">
                                            <video 
                                                // ref={videoRef} 
                                                autoPlay 
                                                playsInline 
                                                muted 
                                                style={{ width: '1px', height: '1px' }}
                                            />
                                            <canvas 
                                                // ref={canvasRef} 
                                                style={{ display: 'none' }}
                                            />
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        {/* QR Code Section */}
                        {qrCodeData && (
                            <div className="flex justify-center">
                                <div className="bg-white/10 backdrop-blur-xl p-8 rounded-2xl border-2 border-white/20 shadow-2xl relative overflow-hidden">
                                    <div className="absolute inset-0 bg-gradient-to-br from-blue-500/10 to-purple-500/10"></div>
                                    <div className="relative">
                                        <div className="text-center mb-4">
                                            <h4 className="font-semibold text-white mb-1">QR Code</h4>
                                            <p className="text-xs text-gray-300">Scan with your authenticator app</p>
                                        </div>
                                        <div className="bg-white p-6 rounded-xl border shadow-lg">
                                            <QRCodeSVG 
                                                value={`otpauth://totp/InkNull:${email}?secret=${secretKey}&issuer=InkNull`}
                                                size={280}
                                                level="M"
                                                includeMargin={true}
                                            />
                                        </div>
                                    </div>
                                </div>
                            </div>
                        )}

                        {/* Manual Entry Section */}
                        <div className="bg-gradient-to-r from-gray-500/20 to-blue-500/20 border border-gray-400/30 rounded-2xl p-6">
                            <div className="flex items-start gap-4">
                                <div className="w-12 h-12 bg-gray-500/20 rounded-full flex items-center justify-center flex-shrink-0">
                                    <i className="pi pi-key text-gray-400 text-xl"></i>
                                </div>
                                <div className="flex-1">
                                    <h3 className="text-lg font-semibold text-white mb-2">Manual Entry (Alternative)</h3>
                                    <p className="text-gray-300 mb-4">
                                        If QR code scanning doesn&apos;t work, you can manually enter the setup key in your authenticator app
                                    </p>
                                    
                                    <div className="bg-white/10 backdrop-blur-xl rounded-lg p-4 border border-white/20">
                                        <div className="flex items-center gap-3">
                                            <div className="flex-1">
                                                <label className="text-xs font-medium text-gray-300 mb-2 block">Setup Key:</label>
                                                <div className="font-mono text-sm bg-white/10 p-3 rounded-lg border border-white/20 break-all text-white">
                                                    {secretKey}
                                                </div>
                                            </div>
                                            <Button 
                                                icon="pi pi-copy" 
                                                className="p-button-sm p-button-outlined bg-white/10 border-white/20 text-white hover:bg-white/20"
                                                onClick={() => {
                                                    navigator.clipboard.writeText(secretKey);
                                                    showMessage('success', 'Copied!', 'Setup key copied to clipboard');
                                                }}
                                                tooltip="Copy to clipboard"
                                            />
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                                {/* Step 1 Action Buttons */}
                                <div className="flex gap-4 justify-end pt-4 border-t border-white/20">
                                    <Button 
                                        label="Cancel Setup" 
                                        severity="secondary" 
                                        className="px-6 py-3 bg-gradient-to-r from-gray-500/20 to-gray-600/20 border border-gray-400/30 text-white hover:from-gray-600/30 hover:to-gray-700/30 hover:scale-105 active:scale-95 transition-all duration-300 rounded-xl"
                                        onClick={() => {
                                            setShowSetup(false);
                                            setQrCodeData(null);
                                            setSecretKey('');
                                            setVerificationCode('');
                                            setSetupStep(1);
                                            // setQrScanned(false); // Removed
                                            // stopQRScanning(); // Removed
                                        }}
                                    />
                                    <Button 
                                        label="Next Step" 
                                        icon="pi pi-arrow-right"
                                        className="px-6 py-3 bg-gradient-to-r from-blue-600 via-purple-600 to-pink-600 hover:from-blue-700 hover:via-purple-700 hover:to-pink-700 border-0 hover:scale-105 active:scale-95 transition-all duration-300 rounded-xl text-white font-semibold shadow-xl"
                                        onClick={() => setSetupStep(2)}
                                    />
                                </div>
                            </div>
                        )}

                        {/* Step 2: Verification */}
                        {setupStep === 2 && (
                            <div className="space-y-6">
                                <div className="bg-gradient-to-r from-purple-500/20 to-pink-500/20 border border-purple-400/30 rounded-2xl p-6">
                            <div className="flex items-start gap-4">
                                        <div className="w-12 h-12 bg-purple-500/20 rounded-full flex items-center justify-center flex-shrink-0">
                                            <i className="pi pi-check-circle text-purple-400 text-xl"></i>
                                </div>
                                <div className="flex-1">
                                            <h3 className="text-xl font-semibold text-white mb-3">Step 2: Verify Setup</h3>
                                            <p className="text-purple-300 mb-4">
                                                Enter the 6-digit code from your authenticator app to verify the setup is working correctly
                                    </p>
                                    
                                            <div className="bg-white/10 backdrop-blur-xl rounded-lg p-6 border border-white/20">
                                                <div className="space-y-4">
                                            <div>
                                                        <label className="text-sm font-medium text-white block mb-3">Verification Code:</label>
                                                <InputText
                                                    value={verificationCode}
                                                    onChange={(e) => setVerificationCode(e.target.value)}
                                                    placeholder="000000"
                                                    maxLength={6}
                                                            className="w-full text-center text-3xl font-mono tracking-widest border-2 border-purple-400/50 rounded-xl p-4 focus:border-purple-400 focus:ring-4 focus:ring-purple-400/20 bg-white/10 backdrop-blur-sm text-white placeholder-gray-400"
                                                    keyfilter="int"
                                                            autoFocus
                                                />
                                            </div>
                                                    <div className="flex items-center gap-2 text-sm text-purple-300">
                                                <i className="pi pi-clock"></i>
                                                <span>Code refreshes every 30 seconds</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                                {/* Step 2 Action Buttons */}
                        <div className="flex gap-4 justify-end pt-4 border-t border-white/20">
                            <Button 
                                        label="Previous Step" 
                                        icon="pi pi-arrow-left"
                                severity="secondary" 
                                className="px-6 py-3 bg-gradient-to-r from-gray-500/20 to-gray-600/20 border border-gray-400/30 text-white hover:from-gray-600/30 hover:to-gray-700/30 hover:scale-105 active:scale-95 transition-all duration-300 rounded-xl"
                                        onClick={() => setSetupStep(1)}
                                    />
                                    <Button 
                                        label="Verify & Continue" 
                                        icon="pi pi-check"
                                        className="px-6 py-3 bg-gradient-to-r from-purple-600 via-pink-600 to-red-600 hover:from-purple-700 hover:via-pink-700 hover:to-red-700 border-0 hover:scale-105 active:scale-95 transition-all duration-300 rounded-xl text-white font-semibold shadow-xl"
                                        onClick={handleTwoFactorVerify}
                                        loading={twoFactorLoading}
                                        disabled={!verificationCode || verificationCode.length !== 6}
                                    />
                                </div>
                            </div>
                        )}

                        {/* Step 3: Completion */}
                        {setupStep === 3 && (
                            <div className="space-y-6">
                                <div className="bg-gradient-to-r from-emerald-500/20 to-green-500/20 border border-emerald-400/30 rounded-2xl p-6">
                                    <div className="flex items-start gap-4">
                                        <div className="w-12 h-12 bg-emerald-500/20 rounded-full flex items-center justify-center flex-shrink-0">
                                            <i className="pi pi-check-circle text-emerald-400 text-xl"></i>
                                        </div>
                                        <div className="flex-1">
                                            <h3 className="text-xl font-semibold text-white mb-3">Step 3: Setup Complete!</h3>
                                            <p className="text-emerald-300 mb-4">
                                                Congratulations! Your two-factor authentication has been successfully enabled. Your account is now protected with an additional layer of security.
                                            </p>
                                            
                                            <div className="bg-white/10 backdrop-blur-xl rounded-lg p-6 border border-white/20">
                                                <div className="space-y-4">
                                                    <div className="flex items-center gap-3 text-emerald-300">
                                                        <i className="pi pi-check-circle"></i>
                                                        <span>QR code scanned successfully</span>
                                                    </div>
                                                    <div className="flex items-center gap-3 text-emerald-300">
                                                        <i className="pi pi-check-circle"></i>
                                                        <span>Verification code confirmed</span>
                                                    </div>
                                                    <div className="flex items-center gap-3 text-emerald-300">
                                                        <i className="pi pi-check-circle"></i>
                                                        <span>2FA enabled for your account</span>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                {/* Step 3 Action Buttons */}
                                <div className="flex gap-4 justify-end pt-4 border-t border-white/20">
                                    <Button 
                                        label="Finish Setup" 
                                        icon="pi pi-check"
                                        className="px-6 py-3 bg-gradient-to-r from-emerald-600 to-green-600 hover:from-emerald-700 hover:to-green-700 border-0 hover:scale-105 active:scale-95 transition-all duration-300 rounded-xl text-white font-semibold shadow-xl"
                                onClick={() => {
                                    setShowSetup(false);
                                    setQrCodeData(null);
                                    setSecretKey('');
                                    setVerificationCode('');
                                            setSetupStep(1);
                                            // setQrScanned(false); // Removed
                                            // stopQRScanning(); // Removed
                                        }}
                            />
                        </div>
                            </div>
                        )}
                    </div>
                )}

                {/* Disable Section */}
                {showDisable && (
                    <div className="space-y-6">
                        <div className="bg-gradient-to-r from-red-500/20 to-orange-500/20 border border-red-400/30 rounded-2xl p-6">
                            <div className="flex items-start gap-4">
                                <div className="w-12 h-12 bg-red-500/20 rounded-full flex items-center justify-center flex-shrink-0">
                                    <i className="pi pi-exclamation-triangle text-red-400 text-xl"></i>
                                </div>
                                <div className="flex-1">
                                    <h3 className="text-lg font-semibold text-white mb-2">Security Warning</h3>
                                    <p className="text-red-300 mb-4">
                                        Disabling two-factor authentication will significantly reduce the security of your account. 
                                        This action should only be performed if you&apos;re experiencing issues with your authenticator app.
                                    </p>
                                </div>
                            </div>
                        </div>

                        <div className="bg-gradient-to-r from-gray-500/20 to-blue-500/20 border border-gray-400/30 rounded-2xl p-6">
                            <div className="flex items-start gap-4">
                                <div className="w-12 h-12 bg-gray-500/20 rounded-full flex items-center justify-center flex-shrink-0">
                                    <i className="pi pi-key text-gray-400 text-xl"></i>
                                </div>
                                <div className="flex-1">
                                    <h3 className="text-lg font-semibold text-white mb-2">Verification Required</h3>
                                    <p className="text-gray-300 mb-4">
                                        To confirm this action, please enter the current 6-digit code from your authenticator app
                                    </p>
                                    
                                    <div className="bg-white/10 backdrop-blur-xl rounded-lg p-4 border border-white/20">
                                        <div className="space-y-3">
                                            <div>
                                                <label className="text-sm font-medium text-white block mb-2">Current Code:</label>
                                                <InputText
                                                    value={verificationCode}
                                                    onChange={(e) => setVerificationCode(e.target.value)}
                                                    placeholder="000000"
                                                    maxLength={6}
                                                    className="w-full text-center text-2xl font-mono tracking-widest border-2 border-red-400/50 rounded-lg p-4 focus:border-red-400 focus:ring-2 focus:ring-red-400/20 bg-white/10 backdrop-blur-sm text-white placeholder-gray-400"
                                                    keyfilter="int"
                                                />
                                            </div>
                                            <div className="flex items-center gap-2 text-sm text-gray-300">
                                                <i className="pi pi-clock"></i>
                                                <span>Code refreshes every 30 seconds</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div className="flex gap-4 justify-end pt-4 border-t border-white/20">
                            <Button 
                                label="Keep 2FA Enabled" 
                                severity="secondary" 
                                className="px-6 py-3 bg-gradient-to-r from-gray-500/20 to-gray-600/20 border border-gray-400/30 text-white hover:from-gray-600/30 hover:to-gray-700/30 hover:scale-105 active:scale-95 transition-all duration-300 rounded-xl"
                                onClick={() => {
                                    setShowDisable(false);
                                    setVerificationCode('');
                                }}
                            />
                            <Button 
                                label="Disable 2FA" 
                                severity="danger"
                                className="px-6 py-3 bg-gradient-to-r from-red-600 via-orange-600 to-pink-600 hover:from-red-700 hover:via-orange-700 hover:to-pink-700 border-0 hover:scale-105 active:scale-95 transition-all duration-300 rounded-xl text-white font-semibold shadow-xl"
                                onClick={handleTwoFactorDisable}
                                loading={twoFactorLoading}
                                disabled={!verificationCode || verificationCode.length !== 6}
                            />
                        </div>
                    </div>
                )}
            </div>
        </div>
    );

    return (
        <section className='w-full flex flex-col p-5 h-[95vh] overflow-y-auto bg-white' >
            <Toast ref={toast} />

            <div className="w-full">
                <div className="mb-8 text-left">
                    <h2 className="text-3xl font-bold text-gray-800 flex items-center justify-start gap-3">
                        <i className="pi pi-user bg-blue-100 rounded-full text-blue-600 text-3xl"></i>
                        Account Settings
                    </h2>
                    <p className="text-gray-500 mt-2">Manage your personal information and security preferences</p>
                </div>

                {/* Profile Section - Responsive Layout */}
                <div className="bg-white rounded-xl shadow-md p-6 md:p-8 mb-8 transition-all hover:shadow-lg border border-gray-100">
                    <div className="flex flex-col xl:flex-row gap-6 mb-4">
                        <div className="flex flex-col items-start xl:items-start xl:flex-shrink-0 mb-6 xl:mb-0 xl:w-auto justify-start xl:justify-start">
                            <div className="relative group">
                                <div className="w-[120px] h-[120px] md:w-[150px] md:h-[150px] xl:w-[180px] xl:h-[180px] rounded-full border-4 border-blue-100 shadow-xl overflow-hidden bg-gradient-to-br from-blue-50 to-purple-50 relative hover:shadow-2xl transition-all duration-300">
                                    <Image 
                                        src={userImage ? (userImage.startsWith('http') ? userImage : `${API_URL}/storage/${userImage}`) : DEFAULT_USER_IMAGE} 
                                        alt="profile"
                                        imageClassName="w-full h-full object-cover transition-transform duration-300 group-hover:scale-110" 
                                        width="180" 
                                        height="180"
                                        preview={false}
                                        onError={(e) => {
                                            e.target.src = DEFAULT_USER_IMAGE;
                                        }}
                                    />
                                    
                                    <div className="absolute inset-0 bg-gradient-to-t from-black/30 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                                    
                                    <div className="absolute inset-0 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-all duration-300">
                                        
                                    </div>
                                </div>
                                
                                <Button 
                                    icon="pi pi-camera" 
                                    className="p-button-rounded p-button-primary absolute -bottom-3 -right-3 shadow-xl hover:shadow-2xl transition-all duration-300 hover:scale-110 border-3 border-white p-button-lg"
                                    tooltip="Change Photo" 
                                    tooltipOptions={{position: 'bottom'}}
                                    onClick={enterEditImageMode}
                                />
                                
                                {imageLoading && (
                                    <div className="absolute inset-0 bg-black/60 rounded-full flex items-center justify-center backdrop-blur-sm">
                                        <div className="animate-spin rounded-full h-10 w-10 border-b-3 border-white"></div>
                                    </div>
                                )}
                                
                                <input
                                    ref={fileInputRef}
                                    type="file"
                                    accept="image/*"
                                    onChange={handleImageUpload}
                                    className="hidden"
                                />
                            </div>
                        </div>
                        <div className="flex-1 min-w-0">
                            {!isEditingProfile ? (
                                <>
                                    <div className="flex justify-between items-center mb-6">
                                        <h3 className="text-xl font-semibold text-gray-800">Profile Information</h3>
                                        <Button icon="pi pi-pencil"
                                            className="p-button-rounded p-button-outlined p-button-primary"
                                            onClick={enterEditProfileMode} />
                                    </div>
                                    <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                                        {/* Name Field */}
                                        <div className="bg-gradient-to-br from-blue-50 to-indigo-50 border border-blue-200 rounded-2xl p-6 shadow-sm hover:shadow-md transition-all duration-300 group">
                                            <div className="flex items-center gap-4 mb-3">
                                                <div className="w-10 h-10 bg-gradient-to-r from-blue-500 to-indigo-500 rounded-xl flex items-center justify-center shadow-lg">
                                                    <i className="pi pi-user text-white text-lg"></i>
                                        </div>
                                                <div>
                                                    <label className="text-sm font-semibold text-blue-700 block">Name</label>
                                                    <p className="text-blue-900 font-bold text-lg">{name || 'No name provided'}</p>
                                        </div>
                                            </div>
                                        </div>
                                        
                                        {/* Email Field */}
                                        <div className="bg-gradient-to-br from-purple-50 to-pink-50 border border-purple-200 rounded-2xl p-6 shadow-sm hover:shadow-md transition-all duration-300 group">
                                            <div className="flex items-center gap-4 mb-3">
                                                <div className="w-10 h-10 bg-gradient-to-r from-purple-500 to-pink-500 rounded-xl flex items-center justify-center shadow-lg">
                                                    <i className="pi pi-envelope text-white text-lg"></i>
                                                </div>
                                                <div>
                                                    <label className="text-sm font-semibold text-purple-700 block">Email</label>
                                                    <p className="text-purple-900 font-bold text-lg">{email || 'No email provided'}</p>
                                                </div>
                                            </div>
                                        </div>
                                        {phone && (
                                            <div className="bg-gradient-to-br from-green-50 to-emerald-50 border border-green-200 rounded-2xl p-6 shadow-sm hover:shadow-md transition-all duration-300 group">
                                                <div className="flex items-center gap-4 mb-3">
                                                    <div className="w-10 h-10 bg-gradient-to-r from-green-500 to-emerald-500 rounded-xl flex items-center justify-center shadow-lg">
                                                        <i className="pi pi-phone text-white text-lg"></i>
                                                    </div>
                                                    <div>
                                                        <label className="text-sm font-semibold text-green-700 block">Phone</label>
                                                        <p className="text-green-900 font-bold text-lg">{phone}</p>
                                                    </div>
                                                </div>
                                            </div>
                                        )}
                                        {position && (
                                            <div className="bg-gradient-to-br from-orange-50 to-amber-50 border border-orange-200 rounded-2xl p-6 shadow-sm hover:shadow-md transition-all duration-300 group">
                                                <div className="flex items-center gap-4 mb-3">
                                                    <div className="w-10 h-10 bg-gradient-to-r from-orange-500 to-amber-500 rounded-xl flex items-center justify-center shadow-lg">
                                                        <i className="pi pi-briefcase text-white text-lg"></i>
                                                    </div>
                                                    <div>
                                                        <label className="text-sm font-semibold text-orange-700 block">Position</label>
                                                        <p className="text-orange-900 font-bold text-lg">{position}</p>
                                                    </div>
                                                </div>
                                            </div>
                                        )}
                                        {customField1 && (
                                            <div className="bg-gradient-to-br from-slate-50 to-gray-50 border border-slate-200 rounded-2xl p-6 shadow-sm hover:shadow-md transition-all duration-300 group">
                                                <div className="flex items-center gap-4 mb-3">
                                                    <div className="w-10 h-10 bg-gradient-to-r from-slate-500 to-gray-500 rounded-xl flex items-center justify-center shadow-lg">
                                                        <i className="pi pi-cog text-white text-lg"></i>
                                                    </div>
                                                    <div>
                                                        <label className="text-sm font-semibold text-slate-700 block">Custom Field 1</label>
                                                        <p className="text-slate-900 font-bold text-lg">{customField1}</p>
                                                    </div>
                                                </div>
                                            </div>
                                        )}
                                        {customField2 && (
                                            <div className="bg-gradient-to-br from-slate-50 to-gray-50 border border-slate-200 rounded-2xl p-6 shadow-sm hover:shadow-md transition-all duration-300 group">
                                                <div className="flex items-center gap-4 mb-3">
                                                    <div className="w-10 h-10 bg-gradient-to-r from-slate-500 to-gray-500 rounded-xl flex items-center justify-center shadow-lg">
                                                        <i className="pi pi-cog text-white text-lg"></i>
                                                    </div>
                                                    <div>
                                                        <label className="text-sm font-semibold text-slate-700 block">Custom Field 2</label>
                                                        <p className="text-slate-900 font-bold text-lg">{customField2}</p>
                                                    </div>
                                                </div>
                                            </div>
                                        )}
                                        {customField3 && (
                                            <div className="bg-gradient-to-br from-slate-50 to-gray-50 border border-slate-200 rounded-2xl p-6 shadow-sm hover:shadow-md transition-all duration-300 group">
                                                <div className="flex items-center gap-4 mb-3">
                                                    <div className="w-10 h-10 bg-gradient-to-r from-slate-500 to-gray-500 rounded-xl flex items-center justify-center shadow-lg">
                                                        <i className="pi pi-cog text-white text-lg"></i>
                                                    </div>
                                                    <div>
                                                        <label className="text-sm font-semibold text-slate-700 block">Custom Field 3</label>
                                                        <p className="text-slate-900 font-bold text-lg">{customField3}</p>
                                                    </div>
                                                </div>
                                            </div>
                                        )}
                                        {customField4 && (
                                            <div className="bg-gradient-to-br from-slate-50 to-gray-50 border border-slate-200 rounded-2xl p-6 shadow-sm hover:shadow-md transition-all duration-300 group">
                                                <div className="flex items-center gap-4 mb-3">
                                                    <div className="w-10 h-10 bg-gradient-to-r from-slate-500 to-gray-500 rounded-xl flex items-center justify-center shadow-lg">
                                                        <i className="pi pi-cog text-white text-lg"></i>
                                                    </div>
                                                    <div>
                                                        <label className="text-sm font-semibold text-slate-700 block">Custom Field 4</label>
                                                        <p className="text-slate-900 font-bold text-lg">{customField4}</p>
                                                    </div>
                                                </div>
                                            </div>
                                        )}
                                        {customField5 && (
                                            <div className="bg-gradient-to-br from-slate-50 to-gray-50 border border-slate-200 rounded-2xl p-6 shadow-sm hover:shadow-md transition-all duration-300 group">
                                                <div className="flex items-center gap-4 mb-3">
                                                    <div className="w-10 h-10 bg-gradient-to-r from-slate-500 to-gray-500 rounded-xl flex items-center justify-center shadow-lg">
                                                        <i className="pi pi-cog text-white text-lg"></i>
                                                    </div>
                                                    <div>
                                                        <label className="text-sm font-semibold text-slate-700 block">Custom Field 5</label>
                                                        <p className="text-slate-900 font-bold text-lg">{customField5}</p>
                                                    </div>
                                                </div>
                                            </div>
                                        )}
                                        {customField6 && (
                                            <div className="bg-gradient-to-br from-slate-50 to-gray-50 border border-slate-200 rounded-2xl p-6 shadow-sm hover:shadow-md transition-all duration-300 group">
                                                <div className="flex items-center gap-4 mb-3">
                                                    <div className="w-10 h-10 bg-gradient-to-r from-slate-500 to-gray-500 rounded-xl flex items-center justify-center shadow-lg">
                                                        <i className="pi pi-cog text-white text-lg"></i>
                                                    </div>
                                                    <div>
                                                        <label className="text-sm font-semibold text-slate-700 block">Custom Field 6</label>
                                                        <p className="text-slate-900 font-bold text-lg">{customField6}</p>
                                                    </div>
                                                </div>
                                            </div>
                                        )}
                                        {customField7 && (
                                            <div className="bg-gradient-to-br from-slate-50 to-gray-50 border border-slate-200 rounded-2xl p-6 shadow-sm hover:shadow-md transition-all duration-300 group">
                                                <div className="flex items-center gap-4 mb-3">
                                                    <div className="w-10 h-10 bg-gradient-to-r from-slate-500 to-gray-500 rounded-xl flex items-center justify-center shadow-lg">
                                                        <i className="pi pi-cog text-white text-lg"></i>
                                                    </div>
                                                    <div>
                                                        <label className="text-sm font-semibold text-slate-700 block">Custom Field 7</label>
                                                        <p className="text-slate-900 font-bold text-lg">{customField7}</p>
                                                    </div>
                                                </div>
                                            </div>
                                        )}
                                        {customField8 && (
                                            <div className="bg-gradient-to-br from-slate-50 to-gray-50 border border-slate-200 rounded-2xl p-6 shadow-sm hover:shadow-md transition-all duration-300 group">
                                                <div className="flex items-center gap-4 mb-3">
                                                    <div className="w-10 h-10 bg-gradient-to-r from-slate-500 to-gray-500 rounded-xl flex items-center justify-center shadow-lg">
                                                        <i className="pi pi-cog text-white text-lg"></i>
                                                    </div>
                                                    <div>
                                                        <label className="text-sm font-semibold text-slate-700 block">Custom Field 8</label>
                                                        <p className="text-slate-900 font-bold text-lg">{customField8}</p>
                                                    </div>
                                                </div>
                                            </div>
                                        )}
                                        {customField9 && (
                                            <div className="bg-gradient-to-br from-slate-50 to-gray-50 border border-slate-200 rounded-2xl p-6 shadow-sm hover:shadow-md transition-all duration-300 group">
                                                <div className="flex items-center gap-4 mb-3">
                                                    <div className="w-10 h-10 bg-gradient-to-r from-slate-500 to-gray-500 rounded-xl flex items-center justify-center shadow-lg">
                                                        <i className="pi pi-cog text-white text-lg"></i>
                                                    </div>
                                                    <div>
                                                        <label className="text-sm font-semibold text-slate-700 block">Custom Field 9</label>
                                                        <p className="text-slate-900 font-bold text-lg">{customField9}</p>
                                                    </div>
                                                </div>
                                            </div>
                                        )}
                                        {customField10 && (
                                            <div className="bg-gradient-to-br from-slate-50 to-gray-50 border border-slate-200 rounded-2xl p-6 shadow-sm hover:shadow-md transition-all duration-300 group">
                                                <div className="flex items-center gap-4 mb-3">
                                                    <div className="w-10 h-10 bg-gradient-to-r from-slate-500 to-gray-500 rounded-xl flex items-center justify-center shadow-lg">
                                                        <i className="pi pi-cog text-white text-lg"></i>
                                                    </div>
                                                    <div>
                                                        <label className="text-sm font-semibold text-slate-700 block">Custom Field 10</label>
                                                        <p className="text-slate-900 font-bold text-lg">{customField10}</p>
                                                    </div>
                                                </div>
                                            </div>
                                        )}
                                    </div>
                                </>
                            ) : (
                                <>
                                    <div className="flex justify-between items-center mb-6">
                                        <h3 className="text-xl font-semibold text-gray-800">Edit Profile</h3>
                                        <Button icon="pi pi-times"
                                            className="p-button-rounded p-button-outlined p-button-danger"
                                            onClick={cancelEditProfile} />
                                    </div>
                                    <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                                        <div className="field bg-gradient-to-br from-blue-50 to-indigo-50 border border-blue-200 rounded-2xl p-6 shadow-sm">
                                            <label className="text-sm font-semibold text-blue-700 block mb-3 flex items-center gap-2">
                                                <i className="pi pi-user text-blue-600"></i>
                                                Name
                                            </label>
                                            <InputText
                                                value={name}
                                                onChange={(e) => {
                                                    setName(e.target.value);
                                                    const error = validateName(e.target.value);
                                                    if (error) {
                                                        setErrors(prev => ({
                                                            ...prev,
                                                            name: error
                                                        }));
                                                    } else {
                                                        clearFieldError('name');
                                                    }
                                                }}
                                                onBlur={(e) => {
                                                    const error = validateName(e.target.value);
                                                    setErrors(prev => ({
                                                        ...prev,
                                                        name: error
                                                    }));
                                                }}
                                                className={`w-full p-inputtext-sm p-4 rounded-xl border-2 focus:border-blue-500 focus:ring-2 focus:ring-blue-200 transition-all duration-200 ${errors.name ? 'border-red-500 bg-red-50' : 'border-blue-300 bg-white'}`}
                                                placeholder="Enter your full name"
                                                maxLength={32}
                                            />
                                            {errors.name && <small className="block mt-2 text-red-600 font-medium">{errors.name}</small>}
                                        </div>
                                        <div className="field bg-gradient-to-br from-purple-50 to-pink-50 border border-purple-200 rounded-2xl p-6 shadow-sm">
                                            <label className="text-sm font-semibold text-purple-700 block mb-3 flex items-center gap-2">
                                                <i className="pi pi-envelope text-purple-600"></i>
                                                Email
                                            </label>
                                            <div className="relative">
                                            <InputText
                                                type="email"
                                                value={email}
                                                                                                    onChange={(e) => {
                                                    setEmail(e.target.value);
                                                    checkEmailAvailability(e.target.value);
                                                    // Clear email error when user starts typing
                                                    if (errors.email) {
                                                        clearFieldError('email');
                                                    }
                                                }}
                                                    onBlur={(e) => {
                                                        const error = validateEmail(e.target.value);
                                                        if (error) {
                                                            setErrors(prev => ({
                                                                ...prev,
                                                                email: error
                                                            }));
                                                        }
                                                    }}
                                                    className={`w-full p-inputtext-sm p-4 rounded-xl border-2 focus:border-purple-500 focus:ring-2 focus:ring-purple-200 transition-all duration-200 ${errors.email ? 'border-red-500 bg-red-50' : 'border-purple-300 bg-white'} ${isEmailChecking ? 'pr-12' : ''}`}
                                                    placeholder="Enter your email address"
                                                    maxLength={40}
                                                />
                                                {isEmailChecking && (
                                                    <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
                                                        <i className="pi pi-spin pi-spinner text-purple-500"></i>
                                                    </div>
                                                )}
                                            </div>
                                            {errors.email && <small className="block mt-2 text-red-600 font-medium">{errors.email}</small>}
                                            {!errors.email && email && email.includes('@') && !isEmailChecking && (
                                                <small className="block mt-2 text-green-600 font-medium">
                                                    ✓ Email address is available
                                                </small>
                                            )}
                                        </div>
                                        <div className="field bg-gradient-to-br from-green-50 to-emerald-50 border border-green-200 rounded-2xl p-6 shadow-sm">
                                            <label className="text-sm font-semibold text-green-700 block mb-3 flex items-center gap-2">
                                                <i className="pi pi-phone text-green-600"></i>
                                                Phone
                                            </label>
                                            
                                            <div className="flex gap-2">
                                                {/* Country Code Dropdown */}
                                                <Dropdown
                                                    value={selectedCountry}
                                                    onChange={(e) => setSelectedCountry(e.value)}
                                                    options={countries}
                                                    optionLabel="name"
                                                    placeholder="Select"
                                                    itemTemplate={countryTemplate}
                                                    valueTemplate={selectedCountryTemplate}
                                                    className={`${classNames({ 'p-invalid': errors.countryCode })}`}
                                                    style={{ minWidth: '140px', maxWidth: '160px' }}
                                                    filter
                                                    filterBy="name,code"
                                                    showClear={false}
                                                />

                                                {/* Phone Number Input */}
                                                <InputText
                                                    value={phone}
                                                    onChange={(e) => {
                                                        const formatted = formatPhoneNumber(e.target.value, selectedCountry.code);
                                                        setPhone(formatted);
                                                        const error = validatePhone(formatted);
                                                        if (error) {
                                                            setErrors(prev => ({
                                                                ...prev,
                                                                phone: error
                                                            }));
                                                        } else {
                                                            clearFieldError('phone');
                                                        }
                                                    }}
                                                    onBlur={(e) => {
                                                        const error = validatePhone(e.target.value);
                                                        setErrors(prev => ({
                                                            ...prev,
                                                            phone: error
                                                        }));
                                                    }}
                                                    className={`flex-1 p-inputtext-sm p-4 rounded-xl border-2 focus:border-green-500 focus:ring-2 focus:ring-green-200 transition-all duration-200 ${errors.phone ? 'border-red-500 bg-red-50' : 'border-green-300 bg-white'}`}
                                                    placeholder="Enter phone number"
                                                />
                                            </div>
                                            
                                            {/* Error Messages */}
                                            {errors.countryCode && <small className="block mt-2 text-red-600 font-medium">{errors.countryCode}</small>}
                                            {errors.phone && <small className="block mt-2 text-red-600 font-medium">{errors.phone}</small>}
                                            
                                            {/* Helper Text */}
                                            <small className="text-green-600 mt-1 block">
                                                Enter phone number without country code
                                            </small>
                                        </div>
                                        <div className="field bg-gradient-to-br from-orange-50 to-amber-50 border border-orange-200 rounded-2xl p-6 shadow-sm">
                                            <label className="text-sm font-semibold text-orange-700 block mb-3 flex items-center gap-2">
                                                <i className="pi pi-briefcase text-orange-600"></i>
                                                Position
                                            </label>
                                            <InputText
                                                value={position}
                                                onChange={(e) => {
                                                    setPosition(e.target.value);
                                                    const error = validatePosition(e.target.value);
                                                    if (error) {
                                                        setErrors(prev => ({
                                                            ...prev,
                                                            position: error
                                                        }));
                                                    } else {
                                                        clearFieldError('position');
                                                    }
                                                }}
                                                onBlur={(e) => {
                                                    const error = validatePosition(e.target.value);
                                                    setErrors(prev => ({
                                                        ...prev,
                                                        position: error
                                                    }));
                                                }}
                                                className={`w-full p-inputtext-sm p-4 rounded-xl border-2 focus:border-orange-500 focus:ring-2 focus:ring-orange-200 transition-all duration-200 ${errors.position ? 'border-red-500 bg-red-50' : 'border-orange-300 bg-white'}`}
                                                placeholder="Enter position"
                                                maxLength={50}
                                            />
                                            {errors.position && <small className="block mt-2 text-red-600 font-medium">{errors.position}</small>}
                                        </div>

                                        <div className="field bg-gradient-to-br from-slate-50 to-gray-50 border border-slate-200 rounded-2xl p-6 shadow-sm">
                                            <label className="text-sm font-semibold text-slate-700 block mb-3 flex items-center gap-2">
                                                <i className="pi pi-cog text-slate-600"></i>
                                                Custom Field 1
                                            </label>
                                            <InputText
                                                value={customField1}
                                                onChange={(e) => {
                                                    setCustomField1(e.target.value);
                                                    const error = validateCustomField(e.target.value, 'Custom Field 1');
                                                    if (error) {
                                                        setErrors(prev => ({
                                                            ...prev,
                                                            custom_field_1: error
                                                        }));
                                                    } else {
                                                        clearFieldError('custom_field_1');
                                                    }
                                                }}
                                                onBlur={(e) => {
                                                    const error = validateCustomField(e.target.value, 'Custom Field 1');
                                                    setErrors(prev => ({
                                                        ...prev,
                                                        custom_field_1: error
                                                    }));
                                                }}
                                                className={`w-full p-inputtext-sm p-4 rounded-xl border-2 focus:border-slate-500 focus:ring-2 focus:ring-slate-200 transition-all duration-200 ${errors.custom_field_1 ? 'border-red-500 bg-red-50' : 'border-slate-300 bg-white'}`}
                                                placeholder="Enter custom field 1"
                                                maxLength={32}
                                            />
                                            {errors.custom_field_1 && <small className="block mt-2 text-red-600 font-medium">{errors.custom_field_1}</small>}
                                        </div>
                                        <div className="field bg-gradient-to-br from-slate-50 to-gray-50 border border-slate-200 rounded-2xl p-6 shadow-sm">
                                            <label className="text-sm font-semibold text-slate-700 block mb-3 flex items-center gap-2">
                                                <i className="pi pi-cog text-slate-600"></i>
                                                Custom Field 2
                                            </label>
                                            <InputText
                                                value={customField2}
                                                onChange={(e) => {
                                                    setCustomField2(e.target.value);
                                                    const error = validateCustomField(e.target.value, 'Custom Field 2');
                                                    if (error) {
                                                        setErrors(prev => ({
                                                            ...prev,
                                                            custom_field_2: error
                                                        }));
                                                    } else {
                                                        clearFieldError('custom_field_2');
                                                    }
                                                }}
                                                className={`w-full p-inputtext-sm p-4 rounded-xl border-2 focus:border-slate-500 focus:ring-2 focus:ring-slate-200 transition-all duration-200 ${errors.custom_field_2 ? 'border-red-500 bg-red-50' : 'border-slate-300 bg-white'}`}
                                                placeholder="Enter custom field 2"
                                            />
                                            {errors.custom_field_2 && <small className="block mt-2 text-red-600 font-medium">{errors.custom_field_2}</small>}
                                        </div>
                                        <div className="field bg-gradient-to-br from-slate-50 to-gray-50 border border-slate-200 rounded-2xl p-6 shadow-sm">
                                            <label className="text-sm font-semibold text-slate-700 block mb-3 flex items-center gap-2">
                                                <i className="pi pi-cog text-slate-600"></i>
                                                Custom Field 3
                                            </label>
                                            <InputText
                                                value={customField3}
                                                onChange={(e) => {
                                                    setCustomField3(e.target.value);
                                                    const error = validateCustomField(e.target.value, 'Custom Field 3');
                                                    if (error) {
                                                        setErrors(prev => ({
                                                            ...prev,
                                                            custom_field_3: error
                                                        }));
                                                    } else {
                                                        clearFieldError('custom_field_3');
                                                    }
                                                }}
                                                className={`w-full p-inputtext-sm p-4 rounded-xl border-2 focus:border-slate-500 focus:ring-2 focus:ring-slate-200 transition-all duration-200 ${errors.custom_field_3 ? 'border-red-500 bg-red-50' : 'border-slate-300 bg-white'}`}
                                                placeholder="Enter custom field 3"
                                            />
                                            {errors.custom_field_3 && <small className="block mt-2 text-red-600 font-medium">{errors.custom_field_3}</small>}
                                        </div>
                                        <div className="field bg-gradient-to-br from-slate-50 to-gray-50 border border-slate-200 rounded-2xl p-6 shadow-sm">
                                            <label className="text-sm font-semibold text-slate-700 block mb-3 flex items-center gap-2">
                                                <i className="pi pi-cog text-slate-600"></i>
                                                Custom Field 4
                                            </label>
                                            <InputText
                                                value={customField4}
                                                onChange={(e) => {
                                                    setCustomField4(e.target.value);
                                                    const error = validateCustomField(e.target.value, 'Custom Field 4');
                                                    if (error) {
                                                        setErrors(prev => ({
                                                            ...prev,
                                                            custom_field_4: error
                                                        }));
                                                    } else {
                                                        clearFieldError('custom_field_4');
                                                    }
                                                }}
                                                className={`w-full p-inputtext-sm p-4 rounded-xl border-2 focus:border-slate-500 focus:ring-2 focus:ring-slate-200 transition-all duration-200 ${errors.custom_field_4 ? 'border-red-500 bg-red-50' : 'border-slate-300 bg-white'}`}
                                                placeholder="Enter custom field 4"
                                            />
                                            {errors.custom_field_4 && <small className="block mt-2 text-red-600 font-medium">{errors.custom_field_4}</small>}
                                        </div>
                                        <div className="field bg-gradient-to-br from-slate-50 to-gray-50 border border-slate-200 rounded-2xl p-6 shadow-sm">
                                            <label className="text-sm font-semibold text-slate-700 block mb-3 flex items-center gap-2">
                                                <i className="pi pi-cog text-slate-600"></i>
                                                Custom Field 5
                                            </label>
                                            <InputText
                                                value={customField5}
                                                onChange={(e) => {
                                                    setCustomField5(e.target.value);
                                                    const error = validateCustomField(e.target.value, 'Custom Field 5');
                                                    if (error) {
                                                        setErrors(prev => ({
                                                            ...prev,
                                                            custom_field_5: error
                                                        }));
                                                    } else {
                                                        clearFieldError('custom_field_5');
                                                    }
                                                }}
                                                className={`w-full p-inputtext-sm p-4 rounded-xl border-2 focus:border-slate-500 focus:ring-2 focus:ring-slate-200 transition-all duration-200 ${errors.custom_field_5 ? 'border-red-500 bg-red-50' : 'border-slate-300 bg-white'}`}
                                                placeholder="Enter custom field 5"
                                            />
                                            {errors.custom_field_5 && <small className="block mt-2 text-red-600 font-medium">{errors.custom_field_5}</small>}
                                        </div>
                                        <div className="field bg-gradient-to-br from-slate-50 to-gray-50 border border-slate-200 rounded-2xl p-6 shadow-sm">
                                            <label className="text-sm font-semibold text-slate-700 block mb-3 flex items-center gap-2">
                                                <i className="pi pi-cog text-slate-600"></i>
                                                Custom Field 6
                                            </label>
                                            <InputText
                                                value={customField6}
                                                onChange={(e) => {
                                                    setCustomField6(e.target.value);
                                                    const error = validateCustomField(e.target.value, 'Custom Field 6');
                                                    if (error) {
                                                        setErrors(prev => ({
                                                            ...prev,
                                                            custom_field_6: error
                                                        }));
                                                    } else {
                                                        clearFieldError('custom_field_6');
                                                    }
                                                }}
                                                className={`w-full p-inputtext-sm p-4 rounded-xl border-2 focus:border-slate-500 focus:ring-2 focus:ring-slate-200 transition-all duration-200 ${errors.custom_field_6 ? 'border-red-500 bg-red-50' : 'border-slate-300 bg-white'}`}
                                                placeholder="Enter custom field 6"
                                            />
                                            {errors.custom_field_6 && <small className="block mt-2 text-red-600 font-medium">{errors.custom_field_6}</small>}
                                        </div>
                                        <div className="field bg-gradient-to-br from-slate-50 to-gray-50 border border-slate-200 rounded-2xl p-6 shadow-sm">
                                            <label className="text-sm font-semibold text-slate-700 block mb-3 flex items-center gap-2">
                                                <i className="pi pi-cog text-slate-600"></i>
                                                Custom Field 7
                                            </label>
                                            <InputText
                                                value={customField7}
                                                onChange={(e) => {
                                                    setCustomField7(e.target.value);
                                                    const error = validateCustomField(e.target.value, 'Custom Field 7');
                                                    if (error) {
                                                        setErrors(prev => ({
                                                            ...prev,
                                                            custom_field_7: error
                                                        }));
                                                    } else {
                                                        clearFieldError('custom_field_7');
                                                    }
                                                }}
                                                className={`w-full p-inputtext-sm p-4 rounded-xl border-2 focus:border-slate-500 focus:ring-2 focus:ring-slate-200 transition-all duration-200 ${errors.custom_field_7 ? 'border-red-500 bg-red-50' : 'border-slate-300 bg-white'}`}
                                                placeholder="Enter custom field 7"
                                            />
                                            {errors.custom_field_7 && <small className="block mt-2 text-red-600 font-medium">{errors.custom_field_7}</small>}
                                        </div>
                                        <div className="field bg-gradient-to-br from-slate-50 to-gray-50 border border-slate-200 rounded-2xl p-6 shadow-sm">
                                            <label className="text-sm font-semibold text-slate-700 block mb-3 flex items-center gap-2">
                                                <i className="pi pi-cog text-slate-600"></i>
                                                Custom Field 8
                                            </label>
                                            <InputText
                                                value={customField8}
                                                onChange={(e) => {
                                                    setCustomField8(e.target.value);
                                                    const error = validateCustomField(e.target.value, 'Custom Field 8');
                                                    if (error) {
                                                        setErrors(prev => ({
                                                            ...prev,
                                                            custom_field_8: error
                                                        }));
                                                    } else {
                                                        clearFieldError('custom_field_8');
                                                    }
                                                }}
                                                className={`w-full p-inputtext-sm p-4 rounded-xl border-2 focus:border-slate-500 focus:ring-2 focus:ring-slate-200 transition-all duration-200 ${errors.custom_field_8 ? 'border-red-500 bg-red-50' : 'border-slate-300 bg-white'}`}
                                                placeholder="Enter custom field 8"
                                            />
                                            {errors.custom_field_8 && <small className="block mt-2 text-red-600 font-medium">{errors.custom_field_8}</small>}
                                        </div>
                                        <div className="field bg-gradient-to-br from-slate-50 to-gray-50 border border-slate-200 rounded-2xl p-6 shadow-sm">
                                            <label className="text-sm font-semibold text-slate-700 block mb-3 flex items-center gap-2">
                                                <i className="pi pi-cog text-slate-600"></i>
                                                Custom Field 9
                                            </label>
                                            <InputText
                                                value={customField9}
                                                onChange={(e) => {
                                                    setCustomField9(e.target.value);
                                                    const error = validateCustomField(e.target.value, 'Custom Field 9');
                                                    if (error) {
                                                        setErrors(prev => ({
                                                            ...prev,
                                                            custom_field_9: error
                                                        }));
                                                    } else {
                                                        clearFieldError('custom_field_9');
                                                    }
                                                }}
                                                className={`w-full p-inputtext-sm p-4 rounded-xl border-2 focus:border-slate-500 focus:ring-2 focus:ring-slate-200 transition-all duration-200 ${errors.custom_field_9 ? 'border-red-500 bg-red-50' : 'border-slate-300 bg-white'}`}
                                                placeholder="Enter custom field 9"
                                            />
                                            {errors.custom_field_9 && <small className="block mt-2 text-red-600 font-medium">{errors.custom_field_9}</small>}
                                        </div>
                                        <div className="field bg-gradient-to-br from-slate-50 to-gray-50 border border-slate-200 rounded-2xl p-6 shadow-sm">
                                            <label className="text-sm font-semibold text-slate-700 block mb-3 flex items-center gap-2">
                                                <i className="pi pi-cog text-slate-600"></i>
                                                Custom Field 10
                                            </label>
                                            <InputText
                                                value={customField10}
                                                onChange={(e) => {
                                                    setCustomField10(e.target.value);
                                                    const error = validateCustomField(e.target.value, 'Custom Field 10');
                                                    if (error) {
                                                        setErrors(prev => ({
                                                            ...prev,
                                                            custom_field_10: error
                                                        }));
                                                    } else {
                                                        clearFieldError('custom_field_10');
                                                    }
                                                }}
                                                className={`w-full p-inputtext-sm p-4 rounded-xl border-2 focus:border-slate-500 focus:ring-2 focus:ring-slate-200 transition-all duration-200 ${errors.custom_field_10 ? 'border-red-500 bg-red-50' : 'border-slate-300 bg-white'}`}
                                                placeholder="Enter custom field 10"
                                            />
                                            {errors.custom_field_10 && <small className="block mt-2 text-red-600 font-medium">{errors.custom_field_10}</small>}
                                        </div>
                                    </div>
                                    <div className="flex gap-3 justify-end mt-6">
                                        <Button label="Cancel" severity="secondary" className="p-button-sm"
                                            onClick={cancelEditProfile} />
                                        <Button 
                                            label="Save Changes" 
                                            className="p-button-sm main-btn"
                                            onClick={handleUpdateProfile}
                                            loading={loading}
                                            disabled={!isFormValid()}
                                        />
                                    </div>

                                    {/* Unified Professional Verification Container */}
                                    {(showPasswordVerification || showEmailVerification) && (
                                        <div className="mt-6 w-full bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50 border-2 border-slate-200 rounded-3xl shadow-2xl overflow-hidden xl:col-span-2">
                                            {/* Header Section */}
                                            <div className="bg-gradient-to-r from-slate-800 to-slate-900 text-white p-6">
                                                <div className="flex items-center justify-between">
                                                    <div className="flex items-center gap-4">
                                                        <div className="w-12 h-12 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full flex items-center justify-center">
                                                            <i className="pi pi-shield text-white text-xl"></i>
                                                        </div>
                                                        <div>
                                                            <h3 className="text-xl font-bold">Profile Update Verification</h3>
                                                            <p className="text-slate-300 text-sm">Secure multi-step verification process</p>
                                                        </div>
                                                    </div>
                                                    <div className="text-right">
                                                        <div className="text-2xl font-bold text-blue-400">
                                                            {showPasswordVerification && !showEmailVerification ? '1' : '2'}/{email !== originalEmail ? '2' : '1'}
                                                        </div>
                                                        <div className="text-xs text-slate-400">Steps</div>
                                                    </div>
                                                </div>
                                            </div>

                                            {/* Progress Section */}
                                            <div className="p-6 border-b border-slate-200 bg-white/50">
                                                <div className="flex items-center justify-between mb-4">
                                                    <h4 className="text-lg font-semibold text-slate-800">Progress</h4>
                                                    <div className="text-sm text-slate-600">
                                                        {showPasswordVerification && !showEmailVerification ? 'Password Verification' : 'Email Verification'}
                                                    </div>
                                                </div>
                                                
                                                {/* Enhanced Progress Bar */}
                                                <div className="w-full bg-slate-200 rounded-full h-4 mb-4 overflow-hidden">
                                                    <div 
                                                        className="bg-gradient-to-r from-blue-500 via-purple-500 to-indigo-500 h-4 rounded-full transition-all duration-700 ease-out shadow-lg"
                                                        style={{ 
                                                            width: `${showPasswordVerification && !showEmailVerification ? '50' : '100'}%` 
                                                        }}
                                                    ></div>
                                                </div>
                                                
                                                {/* Step Indicators */}
                                                <div className="flex items-center justify-between">
                                                    <div className={`flex items-center gap-3 ${showPasswordVerification ? 'text-blue-600' : 'text-slate-400'}`}>
                                                        <div className={`w-10 h-10 rounded-full flex items-center justify-center text-sm font-bold transition-all duration-300 ${
                                                            showPasswordVerification 
                                                                ? 'bg-gradient-to-r from-blue-500 to-blue-600 text-white shadow-lg' 
                                                                : 'bg-slate-300 text-slate-600'
                                                        }`}>
                                                            {showEmailVerification ? <i className="pi pi-check text-sm"></i> : '1'}
                                                        </div>
                                                        <div>
                                                            <div className="text-sm font-semibold">Password</div>
                                                            <div className="text-xs text-slate-500">Verification</div>
                                                        </div>
                                                    </div>
                                                    
                                                    {email !== originalEmail && (
                                                        <>
                                                            <div className={`flex-1 h-1 mx-6 rounded-full transition-all duration-300 ${
                                                                showEmailVerification ? 'bg-gradient-to-r from-blue-500 to-purple-500' : 'bg-slate-300'
                                                            }`}></div>
                                                            
                                                            <div className={`flex items-center gap-3 ${showEmailVerification ? 'text-purple-600' : 'text-slate-400'}`}>
                                                                <div className={`w-10 h-10 rounded-full flex items-center justify-center text-sm font-bold transition-all duration-300 ${
                                                                    showEmailVerification 
                                                                        ? 'bg-gradient-to-r from-purple-500 to-purple-600 text-white shadow-lg' 
                                                                        : 'bg-slate-300 text-slate-600'
                                                                }`}>
                                                                    {showEmailVerification ? '2' : '2'}
                                                                </div>
                                                                <div>
                                                                    <div className="text-sm font-semibold">Email</div>
                                                                    <div className="text-xs text-slate-500">Verification</div>
                                                                </div>
                                    </div>
                                                        </>
                                                    )}
                                                </div>
                                            </div>

                                            {/* Content Section */}
                                            <div className="p-6">
                                                {/* Password Verification Content */}
                                                {showPasswordVerification && !showEmailVerification && (
                                                    <div className="bg-gradient-to-r from-amber-50 to-orange-50 border border-amber-200 rounded-2xl p-6">
                                                        <div className="flex items-start gap-4 mb-6">
                                                            <div className="w-12 h-12 bg-gradient-to-r from-amber-500 to-orange-500 rounded-full flex items-center justify-center shadow-lg">
                                                                <i className="pi pi-lock text-white text-xl"></i>
                                                            </div>
                                                            <div className="flex-1">
                                                                <h4 className="text-lg font-semibold text-amber-800 mb-2">Step 1: Security Verification</h4>
                                                                <p className="text-amber-700 text-sm leading-relaxed">
                                                                    To protect your account, please enter your current password to confirm these changes.
                                                                </p>
                                                            </div>
                                                        </div>
                                                        
                                                        <div className="bg-white rounded-xl p-5 border border-amber-200 shadow-sm">
                                                            <label className="text-sm font-medium text-gray-700 block mb-3">
                                                                <i className="pi pi-lock mr-2 text-amber-600"></i>
                                                                Current Password
                                                            </label>
                                                            <Password
                                                                value={verificationPassword}
                                                                onChange={(e) => setVerificationPassword(e.target.value)}
                                                                onKeyDown={(e) => {
                                                                    if (e.key === 'Enter' && verificationPassword && !loading && !isPasswordBlocked) {
                                                                        handleUpdateProfile();
                                                                    }
                                                                }}
                                                                placeholder="Enter your current password"
                                                                toggleMask
                                                                className={`w-full ${errors.verificationPassword || errors.current_password ? 'p-invalid' : ''}`}
                                                                inputClassName="w-full p-inputtext-sm p-4 rounded-xl border border-gray-300 focus:border-amber-500 focus:ring-2 focus:ring-amber-200 transition-all duration-200"
                                                                feedback={false}
                                                                disabled={isPasswordBlocked || loading}
                                                                autoFocus
                                                                autoComplete="new-password"
                                                                data-form-type="other"
                                                                data-lpignore="true"
                                                                data-1p-ignore="true"
                                                            />
                                                            {(errors.verificationPassword || errors.current_password) && (
                                                                <small className="block mt-2 text-sm text-red-600">
                                                                    {errors.verificationPassword || errors.current_password}
                                                                </small>
                                                            )}
                                                        </div>
                                                    </div>
                                                )}

                                                {/* Email Verification Content */}
                                                {showEmailVerification && (
                                                    <div className="bg-gradient-to-r from-blue-50 to-indigo-50 border border-blue-200 rounded-2xl p-6">
                                                        <div className="flex items-start gap-4 mb-6">
                                                            <div className="w-12 h-12 bg-gradient-to-r from-blue-500 to-indigo-500 rounded-full flex items-center justify-center shadow-lg">
                                                                <i className="pi pi-envelope text-white text-xl"></i>
                                                            </div>
                                                            <div className="flex-1">
                                                                <h4 className="text-lg font-semibold text-blue-800 mb-2">Step 2: Email Verification</h4>
                                                                <p className="text-blue-700 text-sm leading-relaxed">
                                                                    We&apos;ve sent a 6-digit verification code to <strong>{pendingEmailUpdate}</strong>. 
                                                                    Please enter the code to verify your new email address.
                                                                </p>
                                                            </div>
                                                        </div>

                                                        {/* OTP Input Section */}
                                                        <div className="bg-white rounded-xl p-5 border border-blue-200 shadow-sm">
                                                            <div className="mb-4">
                                                                <label className="text-sm font-medium text-gray-700 block mb-3">
                                                                    <i className="pi pi-key mr-2 text-blue-600"></i>
                                                                    Verification Code
                                                                </label>
                                                                <div className="flex gap-3 justify-center">
                                                                    {emailVerificationOtp.map((digit, index) => (
                                                                        <input
                                                                            key={index}
                                                                            ref={emailVerificationInputRefs.current[index]}
                                                                            type="text"
                                                                            maxLength="1"
                                                                            value={digit}
                                                                            onChange={(e) => handleEmailVerificationOtpChange(index, e.target.value)}
                                                                            onKeyDown={(e) => handleEmailVerificationOtpKeyDown(index, e)}
                                                                            onPaste={handleEmailVerificationOtpPaste}
                                                                            className={`w-12 h-12 text-center text-lg font-bold border-2 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 transition-all duration-200 ${
                                                                                emailVerificationActiveInput === index
                                                                                    ? 'border-blue-500 bg-blue-50'
                                                                                    : 'border-gray-300 hover:border-blue-300'
                                                                            }`}
                                                                            disabled={emailVerificationExpired || loading}
                                                                        />
                                                                    ))}
                                                                </div>
                                                            </div>

                                                            {/* Timer and Status */}
                                                            <div className="flex items-center justify-between mb-4">
                                                                <div className="text-sm text-gray-600">
                                                                    {emailVerificationExpired ? (
                                                                        <span className="text-red-600">
                                                                            <i className="pi pi-exclamation-triangle mr-1"></i>
                                                                            Code expired
                                                                        </span>
                                                                    ) : (
                                                                        <span className="text-blue-600">
                                                                            <i className="pi pi-clock mr-1"></i>
                                                                            Expires in {formatEmailVerificationTime(emailVerificationTimeLeft)}
                                                                        </span>
                                                                    )}
                                                                </div>
                                                                <Button
                                                                    label={emailVerificationResendCountdown > 0 ? `Resend in ${emailVerificationResendCountdown}s` : 'Resend Code'}
                                                                    icon="pi pi-refresh"
                                                                    className="px-4 py-2 text-sm bg-gradient-to-r from-blue-500 to-indigo-500 hover:from-blue-600 hover:to-indigo-600 border-0 text-white"
                                                                    onClick={handleEmailVerificationResend}
                                                                    disabled={emailVerificationResendCountdown > 0 || isEmailVerificationResending}
                                                                    loading={isEmailVerificationResending}
                                                                />
                                                            </div>
                                                        </div>
                                                    </div>
                                                )}

                                                {/* Action Buttons */}
                                                <div className="flex gap-3 justify-end mt-6 pt-6 border-t border-slate-200">
                                                    {showPasswordVerification && !showEmailVerification && (
                                                        <>
                                                            <Button
                                                                label="Cancel"
                                                                icon="pi pi-times"
                                                                severity="secondary"
                                                                className="px-6 py-3"
                                                                onClick={cancelEditProfile}
                                                            />
                                                            <Button
                                                                label={
                                                                    isPasswordBlocked ? "Account Temporarily Locked" :
                                                                    "Verify Password & Continue"
                                                                }
                                                                icon={
                                                                    isPasswordBlocked ? "pi pi-ban" :
                                                                    "pi pi-shield"
                                                                }
                                                                className={`px-6 py-3 ${
                                                                    isPasswordBlocked ? 'bg-gradient-to-r from-red-500 to-red-600 border-0 text-white cursor-not-allowed' :
                                                                    'bg-gradient-to-r from-amber-500 to-orange-500 hover:from-amber-600 hover:to-orange-600 border-0 text-white'
                                                                }`}
                                                                onClick={handleUpdateProfile}
                                                                loading={loading} 
                                                                disabled={isPasswordBlocked || !verificationPassword}
                                                            />
                                                        </>
                                                    )}
                                                    
                                                    {showEmailVerification && (
                                                        <>
                                                            <Button
                                                                label="Cancel"
                                                                icon="pi pi-times"
                                                                severity="secondary"
                                                                className="px-6 py-3"
                                                                onClick={() => {
                                                                    setShowEmailVerification(false);
                                                                    setPendingEmailUpdate(null);
                                                                    setEmail(originalEmail);
                                                                    setEmailVerificationOtp(Array(6).fill(''));
                                                                    setEmailVerificationActiveInput(0);
                                                                    // Go back to password verification step
                                                                    setShowPasswordVerification(true);
                                                                }}
                                                            />
                                                            <Button
                                                                label="Verify Email & Save"
                                                                icon="pi pi-check"
                                                                className="px-6 py-3 bg-gradient-to-r from-purple-500 to-indigo-500 hover:from-purple-600 hover:to-indigo-600 border-0 text-white"
                                                                onClick={handleEmailVerificationSubmit}
                                                                disabled={emailVerificationExpired || emailVerificationOtp.some(d => !d)}
                                                            />
                                                        </>
                                                    )}
                                                </div>
                                            </div>
                                        </div>
                                    )}

                                    {/* Security Warning */}
                                    {passwordAttempts > 0 && !isPasswordBlocked && (
                                        <div className="mt-4 p-4 bg-gradient-to-r from-yellow-50 to-orange-50 border border-yellow-200 rounded-lg">
                                            <div className="flex items-center gap-2 text-yellow-800">
                                                <i className="pi pi-exclamation-triangle"></i>
                                                <span className="text-sm font-medium">
                                                    Security Alert: {3 - passwordAttempts} attempts remaining before temporary lockout
                                                </span>
                                            </div>
                                        </div>
                                    )}

                                    {/* Blocked Warning */}
                                    {isPasswordBlocked && (
                                        <div className="mt-4 p-4 bg-gradient-to-r from-red-50 to-pink-50 border border-red-200 rounded-lg">
                                            <div className="flex items-center gap-2 text-red-800">
                                                <i className="pi pi-ban"></i>
                                                <span className="text-sm font-medium">
                                                    Account temporarily locked due to multiple failed attempts. Please wait 5 minutes.
                                                </span>
                                            </div>
                                        </div>
                                    )}
                                </>
                            )}
                        </div>
                    </div>
                </div>

                <div className="w-full border-t border-gray-200 my-10 mx-0"></div>

                <div className="text-left mb-8">
                    <h2 className="text-3xl font-bold text-gray-800 flex items-center justify-start gap-3">
                        <i className="pi pi-lock bg-purple-100 rounded-full text-purple-600 text-3xl"></i>
                        Security Settings
                    </h2>
                    <p className="text-gray-500 mt-2">Protect your account with strong security measures</p>
                </div>

                {renderTwoFactorSection()}

                <div className="mt-8">
                    <div className="bg-white rounded-xl shadow-md p-8 transition-all hover:shadow-lg border border-gray-100">
                    {!isEditingPassword ? (
                        <div className="flex justify-between items-center">
                            <div className="text-left">
                                <h3 className="text-xl font-semibold text-gray-800 mb-2">Password</h3>
                                <p className="text-gray-500">Secure your account with a strong password</p>
                            </div>
                            <Button label="Change Password" icon="pi pi-lock"
                                className="p-button-outlined p-button-primary"
                                onClick={() => setIsEditingPassword(true)} />
                        </div>
                    ) : (
                        <>
                            <div className="flex justify-between items-center mb-6">
                                <h3 className="text-xl font-semibold text-gray-800">Change Password</h3>
                                <Button icon="pi pi-times"
                                    className="p-button-rounded p-button-outlined p-button-danger"
                                    onClick={() => setIsEditingPassword(false)} />
                            </div>
                            <div className="grid grid-cols-1 gap-6">
                                    <div className="field bg-white p-4 rounded-lg">
                                    <label className="text-sm font-medium text-gray-700 block mb-2">Current Password</label>
                                    <Password
                                        value={currentPassword}
                                        onChange={(e) => setCurrentPassword(e.target.value)}
                                        toggleMask
                                        className={`w-full ${errors.current_password ? 'p-invalid' : ''}`}
                                        inputClassName="w-full p-inputtext-sm p-3 rounded-lg border border-gray-300"
                                        feedback={false}
                                        autoComplete="new-password"
                                        data-form-type="other"
                                        data-lpignore="true"
                                        data-1p-ignore="true"
                                    />
                                    {errors.current_password && <small className="block mt-1 text-red-600">{errors.current_password}</small>}
                                </div>
                                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                                        <div className="field bg-white p-4 rounded-lg">
                                        <label className="text-sm font-medium text-gray-700 block mb-2">New Password</label>
                                        <Password
                                            value={newPassword}
                                            onChange={(e) => setNewPassword(e.target.value)}
                                            toggleMask
                                            className={`w-full ${errors.new_password ? 'p-invalid' : ''}`}
                                            inputClassName="w-full p-inputtext-sm p-3 rounded-lg border border-gray-300"
                                            autoComplete="new-password"
                                            data-form-type="other"
                                            data-lpignore="true"
                                            data-1p-ignore="true"
                                        />
                                        {errors.new_password && <small className="block mt-1 text-red-600">{errors.new_password}</small>}
                                    </div>
                                        <div className="field bg-white p-4 rounded-lg">
                                        <label className="text-sm font-medium text-gray-700 block mb-2">Confirm Password</label>
                                        <Password
                                            value={confirmPassword}
                                            onChange={(e) => setConfirmPassword(e.target.value)}
                                            toggleMask
                                            className={`w-full ${errors.confirmPassword || (newPassword !== confirmPassword) ? 'p-invalid' : ''}`}
                                            inputClassName="w-full p-inputtext-sm p-3 rounded-lg border border-gray-300"
                                            autoComplete="new-password"
                                            data-form-type="other"
                                            data-lpignore="true"
                                            data-1p-ignore="true"
                                        />
                                        {errors.confirmPassword && <small className="block mt-1 text-red-600">{errors.confirmPassword}</small>}
                                    </div>
                                </div>
                            </div>
                            <div className="flex gap-3 justify-end mt-6">
                                <Button label="Cancel" severity="secondary" className="p-button-sm"
                                    onClick={() => setIsEditingPassword(false)} />
                                <Button label="Update Password" className="p-button-sm main-btn"
                                    onClick={handleChangePassword}
                                    loading={loading} />
                            </div>
                        </>
                    )}
                </div>
            </div>
            </div>
        </section>
    );
}

export default SettingsIndex;
