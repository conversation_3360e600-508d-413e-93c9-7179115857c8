
import { useState, useEffect } from 'react';
import { useNavigate, useLocation, Outlet, Navigate } from 'react-router-dom';
import axiosInstance from '../config/Axios';

import TwoFactorVerification from '../pages/Auth/TwoFactorVerification';

const AuthChecker = () => {
    const [isChecking, setIsChecking] = useState(true);
    const [requiresTwoFactor, setRequiresTwoFactor] = useState(false);
    const [isAuthenticated, setIsAuthenticated] = useState(false);
    const navigate = useNavigate();
    const location = useLocation();

    useEffect(() => {
        checkAuthStatus();
    }, [location.pathname]); // eslint-disable-line react-hooks/exhaustive-deps

    // Add effect to redirect authenticated users to appropriate dashboard
    useEffect(() => {
        console.log('🔍 AuthChecker redirect effect:', {
            isAuthenticated,
            isChecking,
            requiresTwoFactor,
            pathname: location.pathname,
            userRole: localStorage.getItem('user_role'),
            userType: localStorage.getItem('user_type')
        });
        
        if (isAuthenticated && !isChecking && !requiresTwoFactor) {
            const userType = localStorage.getItem('user_type');
            
            console.log('🔍 AuthChecker redirect - user_type ONLY:', userType);
            
            // If user is on root path, redirect to appropriate dashboard
            if (location.pathname === '/') {
                let redirectUrl = '/manager/dashboard'; // Default for managers
                
                if (userType === 'admin') {
                    redirectUrl = '/admin/dashboard';
                    console.log('🎯 AuthChecker: Admin detected (user_type=admin) - redirecting to admin dashboard');
                } else if (userType === 'manager') {
                    redirectUrl = '/manager/dashboard';
                    console.log('🎯 AuthChecker: Manager detected (user_type=manager) - redirecting to manager dashboard');
                } else {
                    console.log('🎯 AuthChecker: Unknown user_type:', userType, '- using default manager dashboard');
                }
                
                console.log('🚀 AuthChecker: Redirecting to:', redirectUrl);
                navigate(redirectUrl);
            }
        }
    }, [isAuthenticated, isChecking, requiresTwoFactor, location.pathname, navigate]);

    const checkAuthStatus = async () => {
        try {
            // Check if user is logged in
            const token = localStorage.getItem('token');
            const tempToken = localStorage.getItem('temp_token');
            
            console.log('🔍 AuthChecker checkAuthStatus - localStorage data:', {
                token: !!token,
                tempToken: !!tempToken,
                user_role: localStorage.getItem('user_role'),
                user_type: localStorage.getItem('user_type'),
                user_id: localStorage.getItem('user_id'),
                user_name: localStorage.getItem('user_name')
            });
            
            // If no permanent token and no temp token, redirect to login
            if (!token && !tempToken) {
                navigate('/login');
                return;
            }
            
            // If user has temp_token but no permanent token, they're in the middle of 2FA
            // Don't interfere - let the login page handle the 2FA modal
            if (tempToken && !token) {
                navigate('/login');
                return;
            }

            // Check if 2FA verification is required
            const twoFactorVerified = localStorage.getItem('two_factor_verified');
            const verifiedAt = localStorage.getItem('two_factor_verified_at');

            if (twoFactorVerified && verifiedAt) {
                // Check if verification is still valid (within 30 minutes)
                const verifiedTime = new Date(verifiedAt);
                const now = new Date();
                const diffInMinutes = (now - verifiedTime) / (1000 * 60);

                if (diffInMinutes < 30) {
                    // Verification is still valid
                    console.log('✅ AuthChecker: 2FA verification still valid - setting authenticated');
                    setIsAuthenticated(true);
                    setRequiresTwoFactor(false);
                    setIsChecking(false);
                    return;
                }
            }

            // Check 2FA status from server
            try {
                const response = await axiosInstance.get('/two-factor/check-verification');
                
                if (response.data.requires_verification) {
                    console.log('🔐 AuthChecker: Server says 2FA required');
                    setRequiresTwoFactor(true);
                    setIsAuthenticated(false);
                } else {
                    console.log('✅ AuthChecker: Server says no 2FA required - setting authenticated');
                    setIsAuthenticated(true);
                    setRequiresTwoFactor(false);
                }
            } catch (error) {
                // If 2FA check fails, assume user doesn't have 2FA enabled
                console.log('⚠️ AuthChecker: 2FA check failed - assuming no 2FA required');
                setIsAuthenticated(true);
                setRequiresTwoFactor(false);
            }

        } catch (error) {
            console.error('Auth check error:', error);
            navigate('/login');
        } finally {
            setIsChecking(false);
        }
    };

    const handleTwoFactorSuccess = () => {
        console.log('🎉 AuthChecker handleTwoFactorSuccess called');
        setRequiresTwoFactor(false);
        setIsAuthenticated(true);
        
        // Get user type and redirect accordingly (ignore role)
        const userType = localStorage.getItem('user_type');
        
        console.log('🔍 AuthChecker 2FA Success - User data (user_type ONLY):', {
            userType
        });
        
        let redirectUrl = '/manager/dashboard'; // Default for managers
        
        if (userType === 'admin') {
            redirectUrl = '/admin/dashboard';
            console.log('🎯 AuthChecker 2FA Success: Admin detected (user_type=admin) - redirecting to admin dashboard');
        } else if (userType === 'manager') {
            redirectUrl = '/manager/dashboard';
            console.log('🎯 AuthChecker 2FA Success: Manager detected (user_type=manager) - redirecting to manager dashboard');
        } else {
            console.log('🎯 AuthChecker 2FA Success: Unknown user_type:', userType, '- using default manager dashboard');
        }
        
        console.log('🚀 AuthChecker 2FA Success: Redirecting to:', redirectUrl);
        // Redirect to appropriate dashboard
        navigate(redirectUrl);
    };

    // Show loading while checking
    if (isChecking) {
        return (
            <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 flex items-center justify-center">
                <div className="text-center">
                    <div className="w-16 h-16 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center mx-auto mb-4 animate-pulse">
                        <i className="pi pi-shield text-white text-2xl"></i>
                    </div>
                    <h2 className="text-xl font-semibold text-gray-800 mb-2">Verifying Security</h2>
                    <p className="text-gray-600">Please wait while we verify your authentication...</p>
                </div>
            </div>
        );
    }

    // Check if we're in login flow (temp_token exists)
    const tempToken = localStorage.getItem('temp_token');
    
    // Show 2FA verification page if required AND not in login flow
    if (requiresTwoFactor && !tempToken) {
        return <TwoFactorVerification onSuccess={handleTwoFactorSuccess} onClose={() => navigate('/login')} />;
    }
    
    // If temp_token exists, redirect to login to handle 2FA modal there
    if (tempToken) {
        console.log('🔄 AuthChecker: 2FA verification in progress, redirecting to login');
        return <Navigate to="/login" replace />;
    }

    // Show protected content if authenticated
    if (isAuthenticated) {
        return <Outlet />;
    }

    // Redirect to login if not authenticated
    return null;
};

export default AuthChecker;
